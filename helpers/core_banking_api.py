import json
from datetime import timedelta
from typing import Optional

import redis
from decouple import config
from django.conf import settings
from requests import exceptions, request  # type: ignore

core_banking_db = redis.StrictRedis(
    host="localhost",
    port=6379,
    decode_responses=True,
    encoding="utf-8",
)

BASE_URL = "https://banking.libertypayng.com"


class WemaApi:
    headers = {"Content-Type": "application/json"}

    @classmethod
    def request_handler(cls, request_type: str, params: dict):
        """ """
        try:
            response = request(request_type, **params)
            try:
                data = response.json()
            except json.JSONDecodeError:
                data = response.text
            return {
                "status_code": response.status_code,
                "status": True,
                "data": data,
                "error": None,
            }
        except exceptions.RequestException as error:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": str(error),
            }

    @classmethod
    def authenticate(cls):
        """ """
        absolute_url = f"{BASE_URL}/api/v1/companies/auth/login/"
        payload = json.dumps(
            {
                "email": config("CORE_BANKING_USER"),
                "password": config("CORE_BANKING_PASS"),
            }
        )
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            ),
        )
        if response.get("status"):
            response = response.get("data")
            bearer_token = response.get("data").get("access")
            core_banking_db.set(
                "CORE_BANKING_TOKEN",
                bearer_token,
                ex=timedelta(
                    seconds=86400,
                ),
            )
            return bearer_token
        return None

    @classmethod
    def login(cls):
        """ """
        bearer_token = core_banking_db.get("CORE_BANKING_TOKEN")
        if bearer_token is None:
            authenticator = cls.authenticate()
            if authenticator is not None:
                bearer_token = authenticator
                core_banking_db.set(
                    "CORE_BANKING_TOKEN",
                    bearer_token,
                    ex=timedelta(
                        seconds=86400,
                    ),
                )
                return bearer_token
            return None
        return bearer_token

    @classmethod
    def get_one_time_account(cls, request_reference: str):
        """ """
        absolute_url = f"{BASE_URL}/api/v1/core/one_time_account/"
        bearer_token = cls.login()
        if bearer_token is None:
            return "NO BEARER TOKEN"
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps(
            {
                "request_reference": request_reference,
                "provider": "WEMA_BANK",
            }
        )

        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            ),
        )
        return response
    
    @classmethod
    def create_account_number(cls, phone_number: str, first_name: str):
        """ """
        absolute_url = f"{BASE_URL}/api/v1/wema/virtual_accounts/"
        bearer_token = cls.login()
        if bearer_token is None:
            return "NO BEARER TOKEN"
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps(
            {
                "first_name": first_name,
                "phone": phone_number,
            }
        )

        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            ),
        )
        print(response)
        return response
    
    @classmethod
    def verify_send_money(cls, request_reference: str):
        """ """
        absolute_url = f"{BASE_URL}/accounts/verify_transfer?search={request_reference}"
        bearer_token = cls.login()
        if bearer_token is None:
            return "NO BEARER TOKEN"
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps({})
        response = cls.request_handler(
            "GET",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            ),
        )
        return response
