# services.py
import logging
import uuid
from typing import Dict, List

from django.utils import timezone


from .coralpay import CoralPayApi
from .models import WhatsAppSession, WhatsAppScreen
import requests
from django.conf import settings

logger = logging.getLogger(__name__)


class WhatsAppMenuService:
    """Service class for handling WhatsApp menu operations"""

    MAIN_MENU_SERVICES = {
        "1": {"name": "Airtime", "code" : "AIRTIME"},
        "2": {"name": "Data", "code": "DATA"},
        "3": {"name": "Cable TV", "code": "CABLE"},
        "4": {"name": "Electricity", "code": "ELECTRIC"},
        "5": {"name": "Betting", "code": "BET"},
        "6": {"name": "Account Balance", "code": "BALANCE"},
    }

    @classmethod
    def get_biller_service(cls, slug):
        data = CoralPayApi.get_biller_service(slug)
        return data

    @classmethod
    def get_biller_options(cls, slug, current_screen):
        if current_screen == "DATA_OPTIONS":
            data = CoralPayApi.get_biller_data_options(slug)
        elif current_screen == "AIRTIME_OPTIONS":
            data = CoralPayApi.get_biller_airtime_options(slug)
        elif current_screen == "CABLE_OPTIONS":
            data = CoralPayApi.get_biller_cable_options(slug)
        elif current_screen == "ELECTRIC_OPTIONS":
            data = CoralPayApi.get_biller_electric_options(slug)
        elif current_screen == "BET_OPTIONS":
            data = CoralPayApi.get_biller_bet_options(slug)
        else:
            data = CoralPayApi.get_biller_airtime_options(slug)
        return data

    @classmethod
    def get_main_services(cls, user_code: str, phone_number: str) -> Dict:
        """
        Dummy function to get main services list
        In production, this would validate the user_code and phone_number
        """
        from users.models import UserRegistration
        
        logger.info(f"Getting main services for {phone_number} with code {user_code}")

        # Simulate validation
        data = UserRegistration.associate_registration(phone_number=phone_number, whatsapp_id=user_code)
        message = data.get("message")
        services = [
            {"key": key, **value}
            for key, value in cls.MAIN_MENU_SERVICES.items()
        ]

        return {
            "status": "success",
            "message": message,
            "services": services
        }

    @classmethod
    def get_service_options(cls, service_code: str) -> Dict:
        """
        Dummy function to get options for a selected service
        """
        logger.info(f"Getting options for service: {service_code}")
        this_service_code = service_code
        if service_code == "AIRTIME":
            this_service_code = "AIRTIME_AND_DATA"
        elif service_code == "DATA":
            this_service_code = "AIRTIME_AND_DATA"
        elif service_code == "CABLE":
            this_service_code = "PAY_TV"
        elif service_code == "ELECTRIC":
            this_service_code = "ELECTRIC_DISCO"
        elif service_code == "BET":
            this_service_code = "BETTING_AND_LOTTERY"

        service_options = {
            "AIRTIME": cls.get_biller_service(this_service_code),
            "DATA": cls.get_biller_service(this_service_code),
            "CABLE": cls.get_biller_service(this_service_code),
            "ELECTRIC": cls.get_biller_service(this_service_code),
            "BET": cls.get_biller_service(this_service_code),

        }

        if service_code not in service_options:
            return {
                "status": "error",
                "message": "Service not available",
                "options": []
            }

        options = [
            {"key": key, **value}
            for key, value in service_options[service_code].items()
        ]

        service_name = next(
            (s["name"] for s in cls.MAIN_MENU_SERVICES.values()
             if s["code"] == service_code),
            service_code
        )

        return {
            "status": "success",
            "message": f"{service_name} Options - Select:",
            "options": options,
            "service_type": service_code
        }

    @classmethod
    def get_service_options_all(cls, service_code: str, current_screen: str) -> Dict:
        """
        Dummy function to get options for a selected service
        """
        logger.info(f"Getting options for service: {service_code}")
        service_options = {
            f"{service_code}": cls.get_biller_options(service_code, current_screen),
        }

        if service_code not in service_options:
            return {
                "status": "error",
                "message": "Service not available",
                "options": []
            }

        options = [
            {"key": key, **value}
            for key, value in service_options[service_code].items()
        ]

        service_name = next(
            (s["name"] for s in cls.get_biller_options(service_code, current_screen).values()
             if s["slug"] == service_code),
            service_code
        )

        return {
            "status": "success",
            "message": f"{service_name} Options - Select:",
            "options": options,
            "service_type": service_code
        }

    @classmethod
    def process_transaction(cls, service_type: str, option_key: str,
                            phone_number: str, session_data: Dict) -> Dict:
        """
        Dummy function to process the transaction
        """
        logger.info(f"Processing {service_type} transaction for {phone_number}")

        # Generate dummy transaction reference
        transaction_ref = f"VB{uuid.uuid4().hex[:8].upper()}"

        # Simulate transaction processing
        import random
        success = random.choice([True, True, True, False])  # 75% success rate

        if success:
            return {
                "status": "success",
                "message": f"✅ Transaction Successful!\n\nReference: {transaction_ref}\nThank you for using Vendboss!",
                "transaction_ref": transaction_ref,
                "amount": session_data.get("amount", 0),
                "service_type": service_type
            }
        else:
            return {
                "status": "failed",
                "message": f"❌ Transaction Failed!\n\nReference: {transaction_ref}\nPlease try again later or contact support.",
                "transaction_ref": transaction_ref,
                "error_code": "INSUFFICIENT_BALANCE"
            }


class WhatsAppAPIService:
    """Service for sending messages to WhatsApp Business API with dynamic configuration"""

    @staticmethod
    def extract_phone_number_id(webhook_data: dict) -> str:
        """Extract phone_number_id from webhook response"""
        try:
            entry = webhook_data.get('entry', [{}])[0]
            changes = entry.get('changes', [{}])[0]
            value = changes.get('value', {})
            metadata = value.get('metadata', {})
            return metadata.get('phone_number_id', '')
        except (KeyError, IndexError, TypeError):
            return ''

    @staticmethod
    def send_message(phone_number: str, message: str, phone_number_id: str = None) -> dict:
        """Send message to WhatsApp user with dynamic phone_number_id"""
        try:
            # phone_number_id must be provided (extracted from webhook)
            if not phone_number_id:
                return {"status": "error", "message": "Phone number ID not available"}

            # WhatsApp Business API endpoint
            url = f"https://graph.facebook.com/v18.0/{phone_number_id}/messages"

            # Get access token from settings (constant)
            access_token = settings.WHATSAPP_ACCESS_TOKEN
            if not access_token:
                return {"status": "error", "message": "Access token not configured"}

            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            payload = {
                "messaging_product": "whatsapp",
                "to": phone_number,
                "type": "text",
                "text": {
                    "body": message
                }
            }

            response = requests.post(url, json=payload, headers=headers, timeout=30)

            if response.status_code == 200:
                logger.info(f"Message sent successfully to {phone_number}")
                return {"status": "success", "response": response.json()}
            else:
                logger.error(f"Failed to send message to {phone_number}: {response.text}")
                return {"status": "failed", "error": response.text, "status_code": response.status_code}

        except requests.RequestException as e:
            logger.error(f"Error sending WhatsApp message: {str(e)}")
            return {"status": "error", "message": str(e)}

# class WhatsAppSessionService:
#     """Service class for managing WhatsApp sessions"""
#
#     @staticmethod
#     def get_or_create_session(phone_number: str, user_code: str) -> WhatsAppSession:
#         """Get existing active session or create new one"""
#         # Check for existing active session
#         session = WhatsAppSession.objects.filter(
#             phone_number=phone_number,
#             status='ACTIVE'
#         ).first()
#
#         if session and not session.is_expired():
#             # Extend existing session
#             session.extend_session()
#             logger.info(f"Extended existing session for {phone_number}")
#             return session
#         elif session and session.is_expired():
#             # Mark expired session as expired
#             session.status = 'EXPIRED'
#             session.save()
#
#         # Create new session
#         session = WhatsAppSession.objects.create(
#             phone_number=phone_number,
#             user_code=user_code,
#             current_screen='MAIN_MENU'
#         )
#         logger.info(f"Created new session {session.session_id} for {phone_number}")
#         return session
#
#     @staticmethod
#     def update_session_screen(session: WhatsAppSession, screen_name: str,
#                               screen_data: Dict = None, user_input: str = None) -> WhatsAppScreen:
#         """Update session screen and create screen record"""
#         session.current_screen = screen_name
#
#         if screen_data:
#             session.session_data.update(screen_data)
#
#         session.save()
#
#         # Create screen record
#         screen = WhatsAppScreen.objects.create(
#             session=session,
#             screen_name=screen_name,
#             screen_data=screen_data or {},
#             user_input=user_input
#         )
#
#         logger.info(f"Updated session {session.session_id} to screen {screen_name}")
#         return screen
#
#     @staticmethod
#     def cleanup_expired_sessions():
#         """Cleanup expired sessions - can be called via management command"""
#         expired_count = WhatsAppSession.objects.filter(
#             expires_at__lt=timezone.now(),
#             status='ACTIVE'
#         ).update(status='EXPIRED')
#
#         logger.info(f"Marked {expired_count} sessions as expired")
#         return expired_count


class WhatsAppSessionService:
    """Service class for managing WhatsApp sessions"""

    @staticmethod
    def get_active_session(phone_number: str) -> WhatsAppSession:
        """Get existing active session for phone number"""
        session = WhatsAppSession.objects.filter(
            phone_number=phone_number,
            status='ACTIVE'
        ).first()

        if session and not session.is_expired():
            # Session is valid, extend it
            session.extend_session()
            logger.info(f"Found active session for {phone_number}")
            return session
        elif session and session.is_expired():
            # Mark expired session as expired
            session.status = 'EXPIRED'
            session.save()
            logger.info(f"Marked session as expired for {phone_number}")

        return None

    @staticmethod
    def create_new_session(phone_number: str, user_code: str) -> WhatsAppSession:
        """Create a new session"""
        session = WhatsAppSession.objects.create(
            phone_number=phone_number,
            user_code=user_code,
            current_screen='MAIN_MENU'
        )
        logger.info(f"Created new session {session.session_id} for {phone_number}")
        return session

    @staticmethod
    def update_session_screen(session: WhatsAppSession, screen_name: str,
                              screen_data: dict = None, user_input: str = None) -> WhatsAppScreen:
        """Update session screen and create screen record"""

        # Update session
        session.current_screen = screen_name
        if screen_data:
            session.session_data.update(screen_data)
        session.save()

        # Create screen record for tracking
        screen = WhatsAppScreen.objects.create(
            session=session,
            screen_name=screen_name,
            screen_data=screen_data or {},
            user_input=user_input
        )

        logger.info(f"Updated session {session.session_id} to screen {screen_name}")
        return screen

    @staticmethod
    def cleanup_expired_sessions():
        """Cleanup expired sessions - can be called via management command"""
        expired_count = WhatsAppSession.objects.filter(
            expires_at__lt=timezone.now(),
            status='ACTIVE'
        ).update(status='EXPIRED')

        logger.info(f"Marked {expired_count} sessions as expired")
        return expired_count


class WhatsAppResponseService:
    """Service class for formatting WhatsApp responses"""

    @staticmethod
    def format_menu_response(message: str, options: List[Dict],
                             show_back_option: bool = False) -> str:
        """Format menu options into WhatsApp message"""
        response_lines = [message, ""]

        for option in options:
            key = option.get("key", "")
            name = option.get("name", "")
            amount = option.get("amount")
            # data = option.get("data")
            if amount:
                line = f"{key}. {name}: {amount}"
            else:
                line = f"{key}: {name}"

            # if amount and amount != "custom":
            #     line += f" - ₦{amount}"
            # if data:
            #     line += f" ({data})"

            response_lines.append(line)

        if show_back_option and not any(opt.get("key") == "0" for opt in options):
            response_lines.append("0. ← Back to Main Menu")

        response_lines.extend(["", "Reply with your choice number:"])
        return "\n".join(response_lines)
    
    @staticmethod
    def format_back_menu_response(message: str, options: List[Dict],
                             show_back_option: bool = False) -> str:
        """Format menu options into WhatsApp message"""
        response_lines = [message, ""]

        for option in options:
            key = option.get("key", "")
            name = option.get("name", "")
            amount = option.get("amount")
            # data = option.get("data")
            if amount:
                line = f"{key}. {name}: {amount}"
            else:
                line = f"{key}: {name}"

            # if amount and amount != "custom":
            #     line += f" - ₦{amount}"
            # if data:
            #     line += f" ({data})"

            response_lines.append(line)
            response_lines.append(" ")

        if show_back_option and not any(opt.get("key") == "99" for opt in options):
            response_lines.append("99. ← Back to Previous Menu")
            response_lines.append(" ")
            response_lines.append("0. ← Back to Main Menu")

        response_lines.extend(["", "Reply with your choice number:"])
        return "\n".join(response_lines)

    @staticmethod
    def format_error_response(message: str) -> str:
        """Format error message"""
        return f"❌ {message}\n\nPlease try again or contact support."

    @staticmethod
    def format_success_response(message: str) -> str:
        """Format success message"""
        return f"✅ {message}"
