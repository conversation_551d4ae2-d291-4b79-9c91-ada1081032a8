from datetime import datetime

from django.conf import settings
from django.contrib.auth import authenticate
from django.contrib.auth.hashers import check_password
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext as _
import pytz
from rest_framework_simplejwt.tokens import RefreshToken
from typing import Optional
from helpers.reusable import (
    email_sender,
    validate_password,
)
from main.models import BaseModel, OTP
from users.helpers.func import generate_whatsapp_id
from users.managers import UserManager
from users.tasks import create_user_account_wallet


logger = settings.LOGGER


# Create your model(s) here.
class Profile(BaseModel, AbstractBaseUser, PermissionsMixin):
    first_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255, null=True, blank=True)
    phone = models.CharField(max_length=25, unique=True)
    bvn = models.CharField(
        max_length=25,
        unique=True,
        null=True,
        blank=True,
    )
    state = models.CharField(
        max_length=50,
        null=True,
        blank=True,
    )
    email = models.EmailField(
        max_length=255,
        unique=True,
        null=True,
        blank=True,
    )
    password = models.CharField(
        max_length=255,
        validators=[validate_password],
        editable=False,
    )
    email_verified = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_admin = models.BooleanField(default=False)

    objects = UserManager()

    USERNAME_FIELD = "phone"
    REQUIRED_FIELDS = ["email"]

    def __str__(self) -> str:
        return self.phone

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "USER PROFILE"
        verbose_name_plural = "USER PROFILES"
        ordering = ["-created_at"]

    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}"
    

    @staticmethod
    def format_number_from_back_234(phone_number: str) -> str:
        if phone_number is None:
            return None
        phone_number = phone_number.replace("+", "")
        if phone_number.startswith("235") and len(phone_number) == 13:
            return phone_number

        formatted_num = phone_number[-10:]

        if len(formatted_num) != 10 or formatted_num[0] == "0":
            return None
        else:
            return "234" + formatted_num

    @classmethod
    def verify(cls, recipient: str, otp: str) -> dict:
        """
        Verifies the company profile based on the provided OTP.
        Returns:
            dict: A dictionary containing the verification status and message.
        """
        verify = OTP.verify_otp(recipient=recipient, otp=otp)
        if not verify.get("status"):
            return {"status": False, "message": "invalid or expired OTP."}
        else:
            company = cls.objects.filter(email=recipient).first()
            if company is not None:
                company.email_verified = True
                company.save()
                return {
                    "status": True,
                    "message": "company profile was verified successfully.",
                }
            else:
                return {
                    "status": False,
                    "message": "company profile not found.",
                }

    @classmethod
    def sign_in(cls, email: str, password: str) -> dict or None:  # type: ignore
        """
        Authenticates a company with the provided email and password, generating a token for successful sign-in.
        """
        company = authenticate(email=email, password=password)
        if company is None:
            return None
        else:
            if not company.email_verified:
                return {
                    "status": False,
                    "message": "company email is not verified.",
                    "company_id": None,
                    "access": None,
                    "refresh": None,
                }
            else:
                token = RefreshToken.for_user(company)
                company.last_login = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
                company.save()
                return {
                    "status": True,
                    "message": "success",
                    "company_id": company.id,
                    "company_name": company.name,
                    "company_email": company.email,
                    "access": str(token.access_token),
                    "refresh": str(token),
                }

    @classmethod
    def get_details(cls, id: str) -> object:
        """
        Retrieve company details based on the given ID.
        """
        try:
            company = cls.objects.get(id=id)
        except cls.DoesNotExist:
            company = None
        return company
    
    @classmethod
    def fetch_user(cls, phone_number: str) -> object:
        """
        Retrieve company details based on the given ID.
        """
        try:
            user = cls.objects.get(phone=phone_number)
        except cls.DoesNotExist:
            user = None
        return user

    @classmethod
    def change_password(
        cls, company: object, old_password: str, new_password: str
    ) -> dict:
        """
        Change the password of a company.
        """
        verify_password = check_password(old_password, company.password)
        if not verify_password:
            return {
                "status": False,
                "message": "old password is incorrect, forgot password?",
            }
        else:
            # crosscheck passwords for similarities
            if old_password == new_password:
                return {"status": False, "message": "similar password, try a new one."}
            else:
                company.set_password(new_password)
                company.save()
                return {"status": True, "message": "password changed successfully."}

    @classmethod
    def forgot_password(cls, email: str) -> dict:
        """
        Send a password reset OTP to the company's email.
        """
        company = cls.objects.filter(email=email).first()
        if company is not None:
            otp = OTP.get_otp(
                type="PASSWORD RESET", recipient=email, length=6, expiry_time=10
            )
            email_sender(
                recipient=[email],
                subject="Password Reset",
                text=f"""
Hello {company.name.title()},\n
A password reset was requested on your account, complete the process with the code below:\n
{otp}\n
Kindly disregard this email if you didn't request one.\n\n
Best regards,\n
The Team at Liberty Tech X.
                """,
            )
            return {
                "status": True,
                "message": "OTP has been sent to your registered email.",
            }
        else:
            return {"status": False, "message": "company profile not found."}

    @classmethod
    def reset_password(
        cls,
        otp: str,
        new_password: str,
        email: str,
    ):
        """
        Set a new password for an existing company.
        """
        verify = OTP.verify_otp(recipient=email, otp=otp)
        if not verify.get("status"):
            return {"status": False, "message": "invalid or expired OTP."}
        else:
            company = cls.objects.filter(email=email).first()
            if company is not None:
                company.set_password(new_password)
                company.save()
                return {"status": True, "message": "password reset was successful."}
            else:
                return {"status": False, "message": "company profile does not exist."}
            
    @classmethod
    def create_user(
        cls, 
        phone: str,
        first_name: str, 
        last_name: str, 
        state: str, 
        password: str,
        email: str, 
        ) -> "Profile":
        """
        Create a new user profile.
        """
        if Profile.objects.filter(phone=phone).exists():
            return None, "A user with this phone number already exists."
        
        if email:
            if Profile.objects.filter(email=email).exists():
                return None, "A user with this email already exists."
        
        user = Profile.objects.create(
            phone=phone,
            last_name=last_name,
            first_name=first_name,
            state=state,
            email=email,
        )
        user.set_password(password)
        user.save()
        
        return user, "Success"
            

class AssociatedUser(BaseModel):
    """
    Model to handle associated users.
    """
    user = models.ForeignKey(
        Profile,
        on_delete=models.CASCADE,
        related_name="associated_users",
        null=True,
        blank=True,
    )
    phone_number = models.CharField(max_length=25, null=True, blank=True)

    class Meta:
        verbose_name = "ASSOCIATED USER"
        verbose_name_plural = "ASSOCIATED USERS"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.user.phone}-{self.phone_number}"
    
    @classmethod
    def create_associated_user(cls, user: Profile, phone_number: str) -> "AssociatedUser":
        """
        Create a new associated user entry.
        """
        associated_user = cls.objects.create(
            user=user,
            phone_number=phone_number
        )  
        return associated_user 
    

class UserRegistration(BaseModel):
    """
    Model to handle user registration.
    """
    user = models.ForeignKey(
        Profile,
        on_delete=models.CASCADE,
        related_name="user_registration",
        null=True,
        blank=True,
    )
    phone_number = models.CharField(max_length=55,  unique=True, null=True, blank=True)
    store_name = models.CharField(max_length=255, null=True, blank=True)
    whatsapp_id = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        verbose_name = "USER REGISTRATION"
        verbose_name_plural = "USER REGISTRATIONS"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.phone_number} - {self.whatsapp_id}"
    
    @classmethod
    def fetch_merchant_details(cls, phone_number: str):
        from account.models import AccountSystem
        from account.models import Wallet


        user_instance = Profile.objects.filter(phone=phone_number).first()
        web_store_data = cls.objects.filter(phone_number=phone_number).first()

        whatsapp_link = f"https://vendbox.com/{web_store_data.whatsapp_id}"

        vas_store_data = {
            "store_name": web_store_data.store_name if web_store_data else "",
            "whatsapp_link": whatsapp_link
        }


        if user_instance:
            airtime_account = AccountSystem.objects.filter(user=user_instance, account_type="AIRTIME").first()
            general_account = AccountSystem.objects.filter(user=user_instance, account_type="GENERAL").first()
            
            airtime_wallet_balance = Wallet.wallet_balance(user=user_instance, wallet_type="AIRTIME")
            general_wallet_balance = Wallet.wallet_balance(user=user_instance, wallet_type="GENERAL")
            commissions_wallet_balance = Wallet.wallet_balance(user=user_instance, wallet_type="COMMISSIONS")

            airtime_account_data = {
                "account_name": airtime_account.account_name if airtime_account else "",
                "bank_name": airtime_account.bank_name if airtime_account else "",
                "account_number": airtime_account.account_number if airtime_account else "",
                "balance": airtime_wallet_balance
            }
            general_account_data = {
                "account_name": general_account.account_name if general_account else "",
                "bank_name": general_account.bank_name if general_account else "",
                "account_number": general_account.account_number if general_account else "",
                "balance": general_wallet_balance
            }
        else:
            airtime_wallet_balance = Wallet.wallet_balance(user=user_instance, wallet_type="AIRTIME")
            general_wallet_balance = Wallet.wallet_balance(user=user_instance, wallet_type="GENERAL")
            commissions_wallet_balance = Wallet.wallet_balance(user=user_instance, wallet_type="COMMISSIONS")

            airtime_account_data = {
                "account_name": "",
                "bank_name": "",
                "account_number": "",
                "balance": airtime_wallet_balance
            }
            general_account_data = {
                "account_name": "",
                "bank_name": "",
                "account_number": "",
                "balance": general_wallet_balance
            }
        return {
            "vas_store_data": vas_store_data,
            "airtime_account_data": airtime_account_data,
            "general_account_data": general_account_data,
            "commissions_balance": commissions_wallet_balance,
            "message": "successful",
        }
    
    @classmethod
    def fetch_merchant_wallet_balance(cls, phone_number: str):
        from account.models import Wallet

        user_instance = Profile.objects.filter(phone=phone_number).first()

        if user_instance:
            general_wallet_balance = Wallet.wallet_balance(user=user_instance, wallet_type="GENERAL")
            commissions_wallet_balance = Wallet.wallet_balance(user=user_instance, wallet_type="COMMISSIONS")
            status = True
        else:
            commissions_wallet_balance = 0
            general_wallet_balance = 0
            status = False
        return {
            "general_balance": general_wallet_balance,
            "commissions_balance": commissions_wallet_balance,
            "status": status
        }

        
    @classmethod
    def create_registration(
        cls, 
        phone_number: str, 
        email: str,
        first_name: Optional[str] = None, 
        last_name: Optional[str] = None,
        password: Optional[str] = None,
        store_name: Optional[str] = None,
        state: Optional[str] = None
        ) -> "UserRegistration":
        """
        Create a new user registration entry.
        """
        whatsapp_id = generate_whatsapp_id()
        store_registration = cls.objects.filter(phone_number=phone_number).first()
        if store_registration:
            registration = store_registration
        else:
            registration = cls.objects.create(
                phone_number=phone_number,
                store_name=store_name,
                whatsapp_id=whatsapp_id
            )  

        if password is not None:
            user, message = Profile.create_user(
                phone=phone_number,
                email=email,
                first_name=first_name,
                last_name=last_name,
                state=state,
                password=password
            )
            if user:
                registration.store_name = store_name
                registration.user = user
                registration.save()

                create_user_account_wallet(
                    user_id=user.id,
                    store_name=store_name
                    )
    
    @classmethod
    def associate_registration(cls, phone_number: str, whatsapp_id: str) -> "UserRegistration":
        """
        Create a new user registration entry.
        """
        user_instance = cls.objects.filter(whatsapp_id=whatsapp_id).first()
        if user_instance:
            AssociatedUser.create_associated_user(
                user=user_instance.user,
                phone_number=phone_number
            )
            data = {
                "status": True,
                "message": f"Welcome to {user_instance.store_name}! Select an option:"
            }
        else:
            # If no user instance found, create a new registration
            # this_whatsapp_id = generate_whatsapp_id()
            # cls.create_registration(phone_number, first_name=None, whatsapp_id=this_whatsapp_id)
            data = {
                "status": False,
                "message": f"Welcome to VendBox Services! Select an option:"
            }
        return data
    
    @classmethod
    def fetch_associated_user(cls, whatsapp_id: str) -> "UserRegistration":
        """
        Create a new user registration entry.
        """
        user_instance = cls.objects.filter(whatsapp_id=whatsapp_id).first()
        if user_instance:
            associated_user = user_instance.user
        else:
            associated_user = None
        return associated_user
    
    @classmethod
    def fetch_session_user(cls, phone_number: str, whatsapp_id: str) -> "UserRegistration":
        """
        Create a new user registration entry.
        """
        user_instance = cls.objects.filter(whatsapp_id=whatsapp_id, phone_number=phone_number).first()
        if user_instance:
            associated_user = user_instance.user
        else:
            associated_user = None
        return associated_user
    

class ConstantTable(models.Model):
    activation_amount = models.FloatField(default=0.00)

    class Meta:
        verbose_name = "CONSTANT VARIABLE"
        verbose_name_plural = "CONSTANT VARIABLE"

    @classmethod
    def get_constant_instance(cls):
        constant_ins = cls.objects.last()
        if not constant_ins:
            constant_ins = cls.objects.create()
            return constant_ins
        return constant_ins
