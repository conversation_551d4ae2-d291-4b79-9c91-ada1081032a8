import json
from datetime import timed<PERSON>ta
from typing import Optional

import redis
from decouple import config
from django.conf import settings
from requests import exceptions, request  # type: ignore

core_banking_db = redis.StrictRedis(
    host="localhost",
    port=6379,
    decode_responses=True,
    encoding="utf-8",
)

BASE_URL = "https://backend.libertypayng.com"


class LibertyPay:
    headers = {"Content-Type": "application/json"}

    @classmethod
    def request_handler(cls, request_type: str, params: dict):
        """ """
        try:
            response = request(request_type, **params)
            try:
                data = response.json()
            except json.JSONDecodeError:
                data = response.text
            return {
                "status_code": response.status_code,
                "status": True,
                "data": data,
                "error": None,
            }
        except exceptions.RequestException as error:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": str(error),
            }

    @classmethod
    def authenticate(cls):
        """ """
        absolute_url = f"{BASE_URL}/user/login/create/"
        payload = json.dumps(
            {
                "email": config("LIBERTY_PAY_USER"),
                "password": config("LIBERTY_PAY_PASS"),
                "device_type": "MOBILE"
            }
        )
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            ),
        )
        print(response, "RESPONSE:::::::::::::::::")
        if response.get("status"):
            response = response.get("data")
            bearer_token = response.get("access")
            core_banking_db.set(
                "LIBERTY_PAY_TOKEN",
                bearer_token,
                ex=timedelta(
                    seconds=86400,
                ),
            )
            return bearer_token
        return None

    @classmethod
    def login(cls):
        """ """
        bearer_token = core_banking_db.get("LIBERTY_PAY_TOKEN")
        if bearer_token is None:
            authenticator = cls.authenticate()
            if authenticator is not None:
                bearer_token = authenticator
                core_banking_db.set(
                    "LIBERTY_PAY_TOKEN",
                    bearer_token,
                    ex=timedelta(
                        seconds=86400,
                    ),
                )
                return bearer_token
            return None
        return bearer_token

    @classmethod
    def biller_list(cls):
        """ """
        absolute_url = f"{BASE_URL}/accounts/billers_list/"
        bearer_token = cls.login()
        if bearer_token is None:
            return "NO BEARER TOKEN"
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        response = cls.request_handler(
            "GET",
            dict(
                url=absolute_url,
                headers=cls.headers,
                # data={},
            ),
        )
        return response
    
    @classmethod
    def vend_vas_service(cls, payload: dict):
        """ """
        absolute_url = f"{BASE_URL}/accounts/bills_payments/"
        bearer_token = cls.login()
        if bearer_token is None:
            return "NO BEARER TOKEN"
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload["transaction_pin"] = config("LIBERTY_PAY_USER_TRANSACTION_PIN")
        this_payload = json.dumps(
            payload
        )
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=this_payload,
            ),
        )
        return response
    
    @classmethod
    def bills_payment_status(cls, transaction_id: str):
        """ """
        absolute_url = f"{BASE_URL}/accounts/check-bills-payment-status/?transaction_id={transaction_id}"
        api_key = config("LIBERTY_PAY_API_KEY")
        cls.headers["X-API-KEY"] = f"{api_key}"
        response = cls.request_handler(
            "GET",
            dict(
                url=absolute_url,
                headers=cls.headers,
            ),
        )
        print(response, "RESPONSE::::::::::::::")
        return response
    