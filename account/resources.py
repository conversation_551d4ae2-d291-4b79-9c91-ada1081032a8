from import_export import resources

from account import models


class AccountSystemResource(resources.ModelResource):
    class Meta:
        model = models.AccountSystem


class DebitCreditRecordOnAccountResource(resources.ModelResource):
    class Meta:
        model = models.DebitCreditRecordOnAccount

    def dehydrate_user(self, obj):
        return obj.user.phone if obj.user else ""


class TransactionResource(resources.ModelResource):
    class Meta:
        model = models.Transaction

    def dehydrate_user(self, obj):
        return obj.user.phone if obj.user else ""


class WalletResource(resources.ModelResource):
    class Meta:
        model = models.Wallet


class AccountCreationFailureResource(resources.ModelResource):
    class Meta:
        model = models.AccountCreationFailure


class TransactionMetaDataResource(resources.ModelResource):
    class Meta:
        model = models.TransactionMetaData


class CoreBankingCallbackResource(resources.ModelResource):
    class Meta:
        model = models.CoreBankingCallback

class VASTransactionResource(resources.ModelResource):
    class Meta:
        model = models.VASTransaction
