import logging
from pathlib import Path
import  os
from django.core.management.utils import get_random_secret_key
from decouple import Csv, config
from datetime import timedelta

LOGGER = logging.getLogger("__name__")


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY", default=get_random_secret_key(), cast=str)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config("DEBUG", default=False, cast=bool)

ALLOWED_HOSTS = config("ALLOWED_HOSTS", cast=Csv())

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Local app(s).
    "main",
    "users",
    "vendboss",
    "account",
    # Third party app(s).
    "corsheaders",
    "django_celery_beat",
    "django_celery_results",
    "django_filters",
    "rest_framework",
    "rest_framework_simplejwt",
    "drf_yasg",
    "import_export",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"

ENVIRONMENT = config("ENVIRONMENT")

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": config("DATABASE_NAME"),
        "USER": config("DATABASE_USER"),
        "PASSWORD": config("DATABASE_PASSWORD"),
        "HOST": config("DATABASE_HOST"),
        "PORT": config("DATABASE_PORT"),
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

## swagger ui
SWAGGER_SETTINGS = {
    "SECURITY_DEFINITIONS": {
        "Basic": {"type": "basic"},
        "Bearer": {"type": "apiKey", "name": "Authorization", "in": "header"},
    },
    "USE_SESSION_AUTH": True,
    "LOGIN_URL": "admin/",
    "LOGOUT_URL": "admin/logout/",
}
REDOC_SETTINGS = {
    "LAZY_RENDERING": False,
    "FETCH_SCHEMA_WITH_QUERY": True,
    "REQUIRED_PROPS_FIRST": True,
}

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

AUTH_USER_MODEL = "users.Profile"

TOKEN_ERROR_RESPONSE = {
    "detail": "Given token not valid for any token type",
    "code": "token_not_valid",
    "messages": [
        {
            "token_class": "AccessToken",
            "token_type": "access",
            "message": "Token is invalid or expired",
        }
    ],
}

OTP_SECRET = config("OTP_SECRET", cast=str)

# Rest framework config.
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication"
    ],
    "EXCEPTION_HANDLER": "helpers.custom_exceptions.custom_exception_handler",
    "COERCE_DECIMAL_TO_STRING": False,
}

SIMPLE_JWT = {
    # 'ACCESS_TOKEN_LIFETIME': timedelta(seconds=10),
    # 'ACCESS_TOKEN_LIFETIME': timedelta(minutes=50),
    "ACCESS_TOKEN_LIFETIME": (
        timedelta(days=5) if ENVIRONMENT == "prod" else timedelta(days=100)
    ),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=5),
}

SECRET_PASSWORD = config("SECRET_PASSWORD", cast=str)


# CORS
CORS_ALLOW_ALL_ORIGINS = True

CSRF_TRUSTED_ORIGINS = config('CSRF_TRUSTED_ORIGINS', cast=Csv())
CSRF_COOKIE_DOMAIN = config('CSRF_COOKIE_DOMAIN')
SECURE_SSL_REDIRECT = \
    config('SECURE_SSL_REDIRECT', '0').lower() in ['true', 't', '1']
if SECURE_SSL_REDIRECT:
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# CELERY
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_BROKER_URL = "redis://127.0.0.1:6379"
CELERY_RESULT_BACKEND = "django-db"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TASK_SERIALIZER = "json"
CELERY_TIMEZONE = TIME_ZONE

# core banking API credentials
CORE_BANKING_USER = config("CORE_BANKING_USER")
CORE_BANKING_PASS = config("CORE_BANKING_PASS")

LIBERTY_PAY_USER = config("LIBERTY_PAY_USER")
LIBERTY_PAY_PASS = config("LIBERTY_PAY_PASS")
LIBERTY_PAY_USER_TRANSACTION_PIN = config("LIBERTY_PAY_USER_TRANSACTION_PIN")
LIBERTY_PAY_API_KEY = config("LIBERTY_PAY_API_KEY")

VAS_USER_ID = config("VAS_USER_ID")

# WhatsApp Business API Configuration
WHATSAPP_ACCESS_TOKEN = config("WHATSAPP_ACCESS_TOKEN")
PHONE_NUMBER_ID = config("PHONE_NUMBER_ID")
STATIC_ROOT = os.path.join(BASE_DIR, "static")

# WhatsApp Webhook Configuration
WHATSAPP_WEBHOOK_CONFIG = {
    'VERIFY_TOKEN': 'd44d9d156200721257aa0502e9c80d45',
    'SESSION_TIMEOUT_MINUTES': 15,
    'MAX_SCREENS_PER_SESSION': 50,
    'CLEANUP_DAYS': 7,
    'MAX_SESSIONS_PER_PHONE': 5,
}

# Logging configuration for WhatsApp webhook
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'whatsapp_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/whatsapp_webhook.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'whatsapp_webhook': {
            'handlers': ['whatsapp_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

