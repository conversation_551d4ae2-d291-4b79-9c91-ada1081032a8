import json
from datetime import timed<PERSON><PERSON>
from typing import Optional
# from decouple import config
from django.conf import settings
from requests import exceptions, request


BASE_URL = "https://services.libertypayng.com"


class CoralPayApi:
    headers = {"Content-Type": "application/json"}

    @classmethod
    def request_handler(cls, request_type: str, params: dict):
        """ """
        try:
            response = request(request_type, **params)
            try:
                data = response.json()
            except json.JSONDecodeError:
                data = response.text
            return {
                "status_code": response.status_code,
                "status": True,
                "data": data,
                "error": None,
            }
        except exceptions.RequestException as error:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": str(error),
            }

    @classmethod
    def get_biller_service(cls, slug: str):
        """ """
        absolute_url = f"{BASE_URL}/user/billergroupslug/?billerGroupSlug={slug}"

        response = cls.request_handler(
            "GET",
            dict(
                url=absolute_url,
                headers=cls.headers, ),
        )
        data = response.get("data", {}).get("responseData", [])
        billerslug = {}
        if data:
            filtered_data = [d for d in data if d.get("slug") not in ("SMILE", "IPNX")]
            i=1
            for item in filtered_data:
                billerslug.update({f"{i}": {
                      "slug": item["slug"],
                      "name": item["name"],
                     }})
                i+=1
        return billerslug

    @classmethod
    def get_biller_data_options(cls, slug: str):
        """ """
        absolute_url = f"{BASE_URL}/user/billerslug/?billerSlug={slug}"

        response = cls.request_handler(
            "GET",
            dict(
                url=absolute_url,
                headers=cls.headers, ),
        )
        data = response.get("data", {}).get("billerData", [])
        billerslug = {}
        if data:
            filtered_data = [d for d in data if d.get("slug") not in ("MTN_VTU", "AIRTEL_VTU", "GLO_VTU", "9MOBILE_VTU", "SMILE_RECHARGE", "IPNX_VOICE", "IPNX_DATA")]
            i = 1
            for item in filtered_data:
                billerslug.update({f"{i}": {
                    "slug": item["slug"],
                    "name": item["name"],
                    "amount":item["amount"],
                }})
                i += 1
        return billerslug

    @classmethod
    def get_biller_airtime_options(cls, slug: str):
        """ """
        absolute_url = f"{BASE_URL}/user/billerslug/?billerSlug={slug}"

        response = cls.request_handler(
            "GET",
            dict(
                url=absolute_url,
                headers=cls.headers, ),
        )
        data = response.get("data", {}).get("billerData", [])
        billerslug = {}
        if data:
            filtered_data = [d for d in data if d.get("slug") in ("MTN_VTU", "AIRTEL_VTU", "GLO_VTU", "9MOBILE_VTU", "SMILE_RECHARGE", "IPNX_VOICE", "IPNX_DATA")]
            i = 1
            for item in filtered_data:
                billerslug.update({f"{i}": {
                    "slug": item["slug"],
                    "name": item["name"],
                    "amount":item["amount"],
                }})
                i += 1

        return billerslug

    @classmethod
    def get_biller_cable_options(cls, slug: str):
        """ """
        absolute_url = f"{BASE_URL}/user/billerslug/?billerSlug={slug}"

        response = cls.request_handler(
            "GET",
            dict(
                url=absolute_url,
                headers=cls.headers, ),
        )
        data = response.get("data", {}).get("billerData", [])
        billerslug = {}
        if data:
            filtered_data = [d for d in data if d.get("slug") not in ("DSTV_RENEW", "DSTV_TOP_UP", "GOTV_RENEW", "GOTV_TOP_UP")]
            i = 1
            for item in filtered_data:
                billerslug.update({f"{i}": {
                    "slug": item["slug"],
                    "name": item["name"],
                    "amount":item["amount"],
                }})
                i += 1

        return billerslug

    @classmethod
    def get_biller_electric_options(cls, slug: str):
        """ """
        absolute_url = f"{BASE_URL}/user/billerslug/?billerSlug={slug}"

        response = cls.request_handler(
            "GET",
            dict(
                url=absolute_url,
                headers=cls.headers, ),
        )
        data = response.get("data", {}).get("billerData", [])
        print(response)
        billerslug = {}
        if data:
            i = 1
            for item in data:
                billerslug.update({f"{i}": {
                    "slug": item["slug"],
                    "name": item["name"],
                    "amount": item["amount"],
                }})
                i += 1

        return billerslug
    
    @classmethod
    def get_biller_bet_options(cls, slug: str):
        """ """
        absolute_url = f"{BASE_URL}/user/billerslug/?billerSlug={slug}"

        response = cls.request_handler(
            "GET",
            dict(
                url=absolute_url,
                headers=cls.headers, ),
        )
        data = response.get("data", {}).get("billerData", [])
        billerslug = {}
        if data:
            i = 1
            for item in data:
                billerslug.update({f"{i}": {
                    "slug": item["slug"],
                    "name": item["name"],
                    "amount": item["amount"],
                }})
                i += 1

        return billerslug

    @classmethod
    def verify_cable_customer(cls, slug: str, customer_id: str, product_name: str):
        """ """
        absolute_url = f"{BASE_URL}/user/customerlookup_paidtv/"

        payload = json.dumps(
            {
                "billerSlug": slug,
                "customerId": customer_id,
                "productName": product_name
            }
        )
        print("PAYLOAD" ,payload)
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload
            ),
        )
        print("VERIFY CUSTOMER DATA" ,response)
        is_success = response.get("data").get("success", False)
        if is_success is False:
            return None
        else:
             return response

    @classmethod
    def verify_electric_customer(cls, slug: str, customer_id: str, product_name: str):
        """ """
        absolute_url = f"{BASE_URL}/user/customerlookup_paidtv/"

        payload = json.dumps(
            {
                "billerSlug": slug,
                "customerId": customer_id,
                "productName": product_name
            }
        )
        print("PAYLOAD" ,payload)
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload
            ),
        )
        print("VERIFY CUSTOMER DATA" ,response)
        is_success = response.get("data").get("success", False)
        if is_success is False:
            return None
        else:
             return response
        
    @classmethod
    def verify_bet_customer(cls, slug: str, customer_id: str, product_name: str):
        """ """
        absolute_url = f"{BASE_URL}/user/customerlookup_betting/"

        payload = json.dumps(
            {
                "billerSlug": slug,
                "customerId": customer_id,
                "productName": product_name
            }
        )
        print("PAYLOAD" ,payload)
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload
            ),
        )
        print("VERIFY CUSTOMER DATA" ,response)
        is_success = response.get("data").get("success", False)
        if is_success is False:
            return None
        else:
             return response
