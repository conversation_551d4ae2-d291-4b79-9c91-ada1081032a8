from pyexpat.errors import messages

from rest_framework.views import APIView
from rest_framework.response import Response
from django.conf import settings
from rest_framework import status,filters, generics, status

from account.models import Transaction, VASTransaction
from users.models import Profile, UserRegistration
from users.permissions import BlockVPNProxyMiddleware, IsMerchantActivated
from vendboss.coralpay import CoralPayApi
from vendboss.serializers import (
    BillPaymentSerializer, BillPaymentSessionSerializer, ListTransactionHistorySerializer, VendboxProfileSerializer, WhatsAppSession, WhatsAppScreen, WhatsAppTransaction, WhatsAppWebhookRequestSerializer,
    WhatsAppWebhookResponseSerializer, WhatsAppSessionSerializer
)
from rest_framework.permissions import AllowAny
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.shortcuts import get_object_or_404
from vendboss.services import (
    WhatsAppMenuService,
    WhatsAppSessionService,
    WhatsAppResponseService, WhatsAppAPIService
)
import logging
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.exceptions import ValidationError
from rest_framework.filters import SearchFilter
from rest_framework.pagination import PageNumberPagination
# logger = logging.getLogger(__name__)
logger = logging.getLogger('whatsapp_webhook')


class CustomPagination(PageNumberPagination):
    page_size = 100
    page_size_query_param = "page_size"
    max_page_size = 200
    page_query_param = "page"


class VendboxProfileCreateAPIView(APIView):
    """API view to create Vendbox profile"""

    def post(self, request):
        """Create a new Vendbox profile"""
        serializer = VendboxProfileSerializer(data=request.data)

        if serializer.is_valid():
            profile = serializer.save()
            return Response(
                serializer.to_representation(profile),
                status=status.HTTP_201_CREATED
            )

        return Response(
            serializer.errors,
            status=status.HTTP_400_BAD_REQUEST
        )

@method_decorator(csrf_exempt, name='dispatch')
class WhatsAppWebhookAPIView(APIView):
    """
    Enhanced WhatsApp Webhook API View with dynamic configuration extraction
    """
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        """Handle incoming WhatsApp webhook POST requests with dynamic extraction"""
        print("Incoming request data:", request.data)

        # Handle WhatsApp webhook verification or status updates
        if self._is_webhook_verification_or_status(request.data):
            logger.info("Received webhook verification or status update")
            return Response({"status": "ok"}, status=status.HTTP_200_OK)

        serializer = WhatsAppWebhookRequestSerializer(data=request.data)

        if not serializer.is_valid():
            logger.error(f"Invalid webhook data: {serializer.errors}")
            return Response(
                {"error": "Invalid request data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        validated_data = serializer.validated_data
        phone_number = validated_data['phone_number']
        user_message = validated_data['message']
        phone_number_id = validated_data.get('phone_number_id')
        display_phone_number = validated_data.get('display_phone_number')

        logger.info(f"Processing message from {phone_number} using phone_number_id: {phone_number_id}")

        try:
            # Process the message and determine flow
            response_data = self._process_user_message(phone_number, user_message)

            # AUTOMATIC RESPONSE: Send response back to WhatsApp using dynamic phone_number_id
            whatsapp_response = WhatsAppAPIService.send_message(
                phone_number, response_data['message'], phone_number_id
            )

            # Log the WhatsApp API response
            if whatsapp_response.get('status') == 'success':
                logger.info(f"Automatic response sent to {phone_number}")
            else:
                logger.error(f"Failed to send automatic response to {phone_number}: {whatsapp_response}")

            # Create response serializer for API response
            response_serializer = WhatsAppWebhookResponseSerializer(response_data)

            # Add WhatsApp API response status and metadata to the response
            response_data_with_status = response_serializer.data
            response_data_with_status.update({
                'whatsapp_send_status': whatsapp_response.get('status'),
                'phone_number_id': phone_number_id,
                'display_phone_number': display_phone_number,
                'webhook_metadata': {
                    'phone_number_id': phone_number_id,
                    'display_phone_number': display_phone_number
                }
            })

            logger.info(f"Processed message for {phone_number}")
            return Response(response_data_with_status, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}")

            # Send error message to user using dynamic phone_number_id
            error_message = "Sorry, we encountered an error processing your request. Please try again."
            WhatsAppAPIService.send_message(phone_number, error_message, phone_number_id)

            return Response(
                {"error": "Internal server error", "message": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get(self, request, *args, **kwargs):
        """Handle WhatsApp webhook verification (GET request)"""
        verify_token = request.GET.get('hub.verify_token')
        challenge = request.GET.get('hub.challenge')

        # Use the verify token from settings
        expected_verify_token = getattr(settings, 'WHATSAPP_WEBHOOK_CONFIG', {}).get('VERIFY_TOKEN')

        if verify_token == expected_verify_token:
            logger.info(f"Webhook verification successful for token: {verify_token}")
            return Response(int(challenge), status=status.HTTP_200_OK)

        logger.warning(f"Webhook verification failed. Expected: {expected_verify_token}, Got: {verify_token}")
        return Response(
            {"error": "Invalid verify token"},
            status=status.HTTP_403_FORBIDDEN
        )

    def _is_webhook_verification_or_status(self, data) -> bool:
        """Check if this is a webhook verification or status update (not a message)"""
        try:
            entry = data.get('entry', [{}])[0]
            changes = entry.get('changes', [{}])[0]

            # Check if this has messages
            value = changes.get('value', {})
            messages = value.get('messages', [])

            # If no messages, it's likely a status update or verification
            return len(messages) == 0

        except (KeyError, IndexError, TypeError):
            return False

    def _process_user_message(self, phone_number: str, user_message: str) -> dict:
        """Process user message and determine the appropriate flow"""

        # Check if user has an active session
        active_session = WhatsAppSessionService.get_active_session(phone_number)

        if not active_session:
            # No active session - expect six-digit code
            return self._handle_initial_six_digit_code(phone_number, user_message)

        # User has active session - process based on current screen
        return self._process_session_message(active_session, user_message)

    def _handle_initial_six_digit_code(self, phone_number: str, user_message: str) -> dict:
        """Handle the initial six-digit code validation"""

        # Create new session
        session = WhatsAppSessionService.create_new_session(phone_number, user_message)

        # AUTOMATICALLY SHOW MAIN MENU when 6-digit code is valid
        return self._show_main_menu(session)

    def _process_session_message(self, session: WhatsAppSession, user_message: str) -> dict:
        """Process user message based on current screen"""
        current_screen = session.current_screen

        # Handle different screens
        if current_screen == 'MAIN_MENU' or current_screen == 'SERVICE_SELECTION':
            return self._handle_service_selection(session, user_message)

        elif current_screen in ['AIRTIME_OPTIONS', 'DATA_OPTIONS', 'CABLE_OPTIONS', 'ELECTRIC_OPTIONS', 'BET_OPTIONS', 'BALANCE_OPTIONS']:
            return self._handle_service_option_all(session, user_message, current_screen)

        elif current_screen == "DATA_OPTIONS_BUY":
            return self._handle_service_option_data(session, user_message)

        elif current_screen == "AIRTIME_OPTIONS_BUY":
            return self._handle_service_option_airtime(session, user_message)

        elif current_screen == "CABLE_OPTIONS_BUY":
            return self._handle_service_option_cable(session, user_message)

        elif current_screen == "ELECTRIC_OPTIONS_BUY":
            return self._handle_service_option_electric(session, user_message)
        
        elif current_screen == "BET_OPTIONS_BUY":
            return self._handle_service_option_bet(session, user_message)

        elif current_screen == "AIRTIME_OPTIONS_BUY_ID":
            return self._handle_service_airtime_number(session, user_message)

        elif current_screen == "DATA_OPTIONS_BUY_ID":
            return self._handle_service_data_number(session, user_message)

        elif current_screen == "CABLE_OPTIONS_BUY_ID":
            return self._handle_service_cable_number(session, user_message)

        elif current_screen == "ELECTRIC_OPTIONS_BUY_ID":
            return self._handle_service_electric_number(session, user_message)
        
        elif current_screen == "BET_OPTIONS_BUY_ID":
            return self._handle_service_bet_number(session, user_message)

        elif current_screen == "AIRTIME_OPTIONS_BUY_ID_AMOUNT":
            return self._handle_airtime_custom_amount(session, user_message)

        elif current_screen == "ELECTRIC_OPTIONS_BUY_ID_AMOUNT":
            return self._handle_electric_custom_amount(session, user_message)
        
        elif current_screen == "BET_OPTIONS_BUY_ID_AMOUNT":
            return self._handle_bet_custom_amount(session, user_message)

        elif current_screen == 'CUSTOM_AMOUNT':
            return self._handle_custom_amount(session, user_message)

        elif current_screen == 'CONFIRM_TRANSACTION':
            return self._handle_transaction_confirmation(session, user_message)
        
        elif current_screen == 'CONFIRM_TRANSACTION_DATA':
            return self._handle_transaction_confirmation_data(session, user_message)
        
        elif current_screen == 'CONFIRM_TRANSACTION_AIRTIME':
            return self._handle_transaction_confirmation_airtime(session, user_message)
        
        elif current_screen == 'CONFIRM_TRANSACTION_ELECTRIC':
            return self._handle_transaction_confirmation_electric(session, user_message)
        
        elif current_screen == 'CONFIRM_TRANSACTION_CABLE':
            return self._handle_transaction_confirmation_cable(session, user_message)
        
        elif current_screen == 'CONFIRM_TRANSACTION_BET':
            return self._handle_transaction_confirmation_bet(session, user_message)
        
        elif current_screen == 'COMPLETED' or current_screen == 'CANCELLED':
            return self._handle_transaction_confirmation_bet(session, user_message)
        
        else:
            # Default to main menu for unknown screens
            return self._show_main_menu(session)

    def _show_main_menu(self, session: WhatsAppSession) -> dict:
        """Display main menu to user - AUTOMATICALLY TRIGGERED"""

        # Get services from service class
        services_data = WhatsAppMenuService.get_main_services(
            session.user_code, session.phone_number
        )

        if services_data['status'] == 'error':
            message = WhatsAppResponseService.format_error_response(
                services_data['message']
            )
        else:
            message = WhatsAppResponseService.format_menu_response(
                services_data['message'], services_data['services']
            )

        # Update session screen
        screen_record = WhatsAppSessionService.update_session_screen(
            session, 'SERVICE_SELECTION',
            {'services': services_data.get('services', [])},
            None  # No user input for initial menu display
        )

        return {
            'phone_number': session.phone_number,
            'message': message,
            'session_id': session.session_id,
            'screen': 'SERVICE_SELECTION',
            'options': services_data.get('services', []),
            'previous_option': services_data
        }

    def _handle_service_selection(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid number"
            )
            return self._create_response(session, message, 'SERVICE_SELECTION')

        # Get selected service
        services = session.session_data.get('services', [])
        selected_service = next(
            (s for s in services if s['key'] == user_input), None
        )

        if not selected_service:
            message = WhatsAppResponseService.format_error_response(
                "Invalid selection. Please choose from the available options."
            )
            return self._create_response(session, message, 'SERVICE_SELECTION')

        service_code = selected_service['code']
        
        # Handle special services
        if service_code == 'BALANCE':
            return self._handle_balance_check(session, user_input)
        
        # Get service options - AUTOMATIC RESPONSE
        options_data = WhatsAppMenuService.get_service_options(service_code)

        if options_data['status'] == 'error':
            message = WhatsAppResponseService.format_error_response(
                options_data['message']
            )
            return self._create_response(session, message, 'SERVICE_SELECTION')

        message = WhatsAppResponseService.format_menu_response(
            options_data['message'], options_data['options'], show_back_option=True
        )

        # Update session
        screen_name = f"{service_code}_OPTIONS"
        WhatsAppSessionService.update_session_screen(
            session, screen_name,
            {
                'service_type': service_code,
                'options': options_data['options'],
                'selected_service': selected_service,
                'previous_options': options_data['options'],
                'previous_user_input': user_input,
                'previous_selected_service': selected_service
            },
            user_input
        )

        return {
            'phone_number': session.phone_number,
            'message': message,
            'session_id': session.session_id,
            'screen': screen_name,
            'options': options_data['options']
        }

    def _handle_service_option_all(self, session: WhatsAppSession, user_input: str, current_screen: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        # Validate input
        if user_input == "0":
            services_data = WhatsAppMenuService.get_main_services(
                session.user_code, session.phone_number
            )
            WhatsAppSessionService.update_session_screen(
                session, 'SERVICE_SELECTION',
                {'services': services_data.get('services', [])},
                None  # No user input for initial menu display
            )
            
            services_data = WhatsAppMenuService.get_main_services(
                session.user_code, session.phone_number
            )

            if services_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    services_data['message']
                )
            else:
                message = WhatsAppResponseService.format_menu_response(
                    services_data['message'], services_data['services']
                )
            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': 'SERVICE_SELECTION',
                'options': services_data.get('services', [])
            }
        
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid number"
            )
            return self._create_response(session, message, session.current_screen)
        
        # Get selected service
        services = session.session_data.get('options', [])
        selected_service = next(
            (s for s in services if s['key'] == user_input), None
        )

        if not selected_service:
            message = WhatsAppResponseService.format_error_response(
                "Invalid selection. Please choose from the available options."
            )
            return self._create_response(session, message, session.current_screen)

        service_code = selected_service['slug']

        product_name = selected_service["slug"].split("_")[0]

        # Get service options - AUTOMATIC RESPONSE
        options_data = WhatsAppMenuService.get_service_options_all(service_code, current_screen)

        if options_data['status'] == 'error':
            message = WhatsAppResponseService.format_error_response(
                options_data['message']
            )
            return self._create_response(session, message, session.current_screen)

        message = WhatsAppResponseService.format_back_menu_response(
            options_data['message'], options_data['options'], show_back_option=True
        )

        if session.current_screen == "AIRTIME_OPTIONS":
            screen_name = f"{session.current_screen}_BUY_ID"
            WhatsAppSessionService.update_session_screen(
                session, screen_name,
                {
                    'service_type': service_code,
                    'product_name': product_name,
                    'buy_options': selected_service
                },
                user_input
            )

            return self._handle_transaction_phone_number_request(session, selected_service)
        
        # Update session
        screen_name = f"{session.current_screen}_BUY"
        WhatsAppSessionService.update_session_screen(
            session, screen_name,
            {
                'service_type': service_code,
                'options': options_data['options'],
                'selected_service': selected_service,
                'product_name': product_name
            },
            user_input
        )

        return {
            'phone_number': session.phone_number,
            'message': message,
            'session_id': session.session_id,
            'screen': screen_name,
            'options': options_data['options']
        }
    
    def _handle_service_option_data(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""
        if user_input == "0":
            
            services_data = WhatsAppMenuService.get_main_services(
                session.user_code, session.phone_number
            )

            WhatsAppSessionService.update_session_screen(
                session, 'SERVICE_SELECTION',
                {'services': services_data.get('services', [])},
                None  # No user input for initial menu display
            )

            if services_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    services_data['message']
                )
            else:
                message = WhatsAppResponseService.format_menu_response(
                    services_data['message'], services_data['services']
                )
            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': 'SERVICE_SELECTION',
                'options': services_data.get('services', [])
            }
        
        if user_input == "99":
            previous_user_input = session.session_data.get("previous_user_input")
            previous_options = session.session_data.get("previous_options")
            previous_selected_service = session.session_data.get("previous_selected_service")
            service_code = "_".join(session.current_screen.split("_")[:-2])

            options_data = WhatsAppMenuService.get_service_options(service_code)

            screen_name = f"{service_code}_OPTIONS"
            WhatsAppSessionService.update_session_screen(
                session, screen_name,
                {
                    'service_type': service_code,
                    'options': previous_options,
                    'selected_service': previous_selected_service
                },
                previous_user_input
            )

            if options_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    options_data['message']
                )
                return self._create_response(session, message, session.current_screen)

            message = WhatsAppResponseService.format_menu_response(
                options_data['message'], options_data['options'], show_back_option=True
            )

            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': screen_name,
                'options': options_data['options']
            }
        
        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid number"
            )
            return self._create_response(session, message, session.current_screen)

        # Get selected service
        this_selected_service = session.session_data.get('options', [])
        selected_service = next(
            (s for s in this_selected_service if s['key'] == user_input), None
        )

        if not selected_service:
            message = WhatsAppResponseService.format_error_response(
                "Invalid selection. Please choose from the available options."
            )
            return self._create_response(session, message, session.current_screen)

        service_code = selected_service['slug']

        screen_name = f"{session.current_screen}_ID"
        WhatsAppSessionService.update_session_screen(
            session, screen_name,
            {
                'buy_options': selected_service
            },
            user_input
        )

        return self._handle_transaction_phone_number_request(session, selected_service)

    def _handle_service_option_cable(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        if user_input == "0":
            
            services_data = WhatsAppMenuService.get_main_services(
                session.user_code, session.phone_number
            )

            WhatsAppSessionService.update_session_screen(
                session, 'SERVICE_SELECTION',
                {'services': services_data.get('services', [])},
                None  # No user input for initial menu display
            )

            if services_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    services_data['message']
                )
            else:
                message = WhatsAppResponseService.format_menu_response(
                    services_data['message'], services_data['services']
                )
            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': 'SERVICE_SELECTION',
                'options': services_data.get('services', [])
            }
        
        if user_input == "99":
            previous_user_input = session.session_data.get("previous_user_input")
            previous_options = session.session_data.get("previous_options")
            previous_selected_service = session.session_data.get("previous_selected_service")
            service_code = "_".join(session.current_screen.split("_")[:-2])

            options_data = WhatsAppMenuService.get_service_options(service_code)

            screen_name = f"{service_code}_OPTIONS"
            WhatsAppSessionService.update_session_screen(
                session, screen_name,
                {
                    'service_type': service_code,
                    'options': previous_options,
                    'selected_service': previous_selected_service
                },
                previous_user_input
            )

            if options_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    options_data['message']
                )
                return self._create_response(session, message, session.current_screen)

            message = WhatsAppResponseService.format_menu_response(
                options_data['message'], options_data['options'], show_back_option=True
            )

            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': screen_name,
                'options': options_data['options']
            }
        
        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid number"
            )
            return self._create_response(session, message, session.current_screen)

        # Get selected service
        this_selected_service = session.session_data.get('options', [])
        selected_service = next(
            (s for s in this_selected_service if s['key'] == user_input), None
        )

        if not selected_service:
            message = WhatsAppResponseService.format_error_response(
                "Invalid selection. Please choose from the available options."
            )
            return self._create_response(session, message, session.current_screen)

        service_code = selected_service['slug']

        screen_name = f"{session.current_screen}_ID"
        WhatsAppSessionService.update_session_screen(
            session, screen_name,
            {
                'buy_options': selected_service
            },
            user_input
        )

        return self._handle_transaction_cable_number_request(session, selected_service)

    def _handle_service_option_airtime(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        if user_input == "0":
            
            services_data = WhatsAppMenuService.get_main_services(
                session.user_code, session.phone_number
            )

            WhatsAppSessionService.update_session_screen(
                session, 'SERVICE_SELECTION',
                {'services': services_data.get('services', [])},
                None  # No user input for initial menu display
            )

            if services_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    services_data['message']
                )
            else:
                message = WhatsAppResponseService.format_menu_response(
                    services_data['message'], services_data['services']
                )
            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': 'SERVICE_SELECTION',
                'options': services_data.get('services', [])
            }
        
        if user_input == "99":
            previous_user_input = session.session_data.get("previous_user_input")
            previous_options = session.session_data.get("previous_options")
            previous_selected_service = session.session_data.get("previous_selected_service")
            service_code = "_".join(session.current_screen.split("_")[:-2])

            options_data = WhatsAppMenuService.get_service_options(service_code)

            screen_name = f"{service_code}_OPTIONS"
            WhatsAppSessionService.update_session_screen(
                session, screen_name,
                {
                    'service_type': service_code,
                    'options': previous_options,
                    'selected_service': previous_selected_service
                },
                previous_user_input
            )

            if options_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    options_data['message']
                )
                return self._create_response(session, message, session.current_screen)

            message = WhatsAppResponseService.format_menu_response(
                options_data['message'], options_data['options'], show_back_option=True
            )

            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': screen_name,
                'options': options_data['options']
            }
        
        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid number"
            )
            return self._create_response(session, message, session.current_screen)
        
        # Get selected service
        this_selected_service = session.session_data.get('options', [])
        selected_service = next(
            (s for s in this_selected_service if s['key'] == user_input), None
        )

        if not selected_service:
            message = WhatsAppResponseService.format_error_response(
                "Invalid selection. Please choose from the available options."
            )
            return self._create_response(session, message, session.current_screen)


        screen_name = f"{session.current_screen}_ID"
        WhatsAppSessionService.update_session_screen(
            session, screen_name,
            {
                'buy_options': selected_service
            },
            user_input
        )

        return self._handle_transaction_phone_number_request(session, selected_service)

    def _handle_service_option_electric(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        if user_input == "0":
            
            services_data = WhatsAppMenuService.get_main_services(
                session.user_code, session.phone_number
            )

            WhatsAppSessionService.update_session_screen(
                session, 'SERVICE_SELECTION',
                {'services': services_data.get('services', [])},
                None  # No user input for initial menu display
            )

            if services_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    services_data['message']
                )
            else:
                message = WhatsAppResponseService.format_menu_response(
                    services_data['message'], services_data['services']
                )
            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': 'SERVICE_SELECTION',
                'options': services_data.get('services', [])
            }
        
        if user_input == "99":
            previous_user_input = session.session_data.get("previous_user_input")
            previous_options = session.session_data.get("previous_options")
            previous_selected_service = session.session_data.get("previous_selected_service")
            service_code = "_".join(session.current_screen.split("_")[:-2])

            options_data = WhatsAppMenuService.get_service_options(service_code)

            screen_name = f"{service_code}_OPTIONS"
            WhatsAppSessionService.update_session_screen(
                session, screen_name,
                {
                    'service_type': service_code,
                    'options': previous_options,
                    'selected_service': previous_selected_service
                },
                previous_user_input
            )

            if options_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    options_data['message']
                )
                return self._create_response(session, message, session.current_screen)

            message = WhatsAppResponseService.format_menu_response(
                options_data['message'], options_data['options'], show_back_option=True
            )

            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': screen_name,
                'options': options_data['options']
            }
        
        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid number"
            )
            return self._create_response(session, message, session.current_screen)

        # Get selected service
        this_selected_service = session.session_data.get('options', [])
        selected_service = next(
            (s for s in this_selected_service if s['key'] == user_input), None
        )

        if not selected_service:
            message = WhatsAppResponseService.format_error_response(
                "Invalid selection. Please choose from the available options."
            )
            return self._create_response(session, message, session.current_screen)

        service_code = selected_service['slug']

        screen_name = f"{session.current_screen}_ID"
        WhatsAppSessionService.update_session_screen(
            session, screen_name,
            {
                'buy_options': selected_service
            },
            user_input
        )

        return self._handle_transaction_electric_number_request(session, selected_service)
    
    def _handle_service_option_bet(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        if user_input == "0":
            
            services_data = WhatsAppMenuService.get_main_services(
                session.user_code, session.phone_number
            )

            WhatsAppSessionService.update_session_screen(
                session, 'SERVICE_SELECTION',
                {'services': services_data.get('services', [])},
                None  # No user input for initial menu display
            )

            if services_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    services_data['message']
                )
            else:
                message = WhatsAppResponseService.format_menu_response(
                    services_data['message'], services_data['services']
                )
            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': 'SERVICE_SELECTION',
                'options': services_data.get('services', [])
            }
        
        if user_input == "99":
            previous_user_input = session.session_data.get("previous_user_input")
            previous_options = session.session_data.get("previous_options")
            previous_selected_service = session.session_data.get("previous_selected_service")
            service_code = "_".join(session.current_screen.split("_")[:-2])

            options_data = WhatsAppMenuService.get_service_options(service_code)

            screen_name = f"{service_code}_OPTIONS"
            WhatsAppSessionService.update_session_screen(
                session, screen_name,
                {
                    'service_type': service_code,
                    'options': previous_options,
                    'selected_service': previous_selected_service
                },
                previous_user_input
            )

            if options_data['status'] == 'error':
                message = WhatsAppResponseService.format_error_response(
                    options_data['message']
                )
                return self._create_response(session, message, session.current_screen)

            message = WhatsAppResponseService.format_menu_response(
                options_data['message'], options_data['options'], show_back_option=True
            )

            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': screen_name,
                'options': options_data['options']
            }
        
        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid number"
            )
            return self._create_response(session, message, session.current_screen)

        # Get selected service
        this_selected_service = session.session_data.get('options', [])
        selected_service = next(
            (s for s in this_selected_service if s['key'] == user_input), None
        )
        if not selected_service:
            message = WhatsAppResponseService.format_error_response(
                "Invalid selection. Please choose from the available options."
            )
            return self._create_response(session, message, session.current_screen)

        service_code = selected_service['slug']

        screen_name = f"{session.current_screen}_ID"
        WhatsAppSessionService.update_session_screen(
            session, screen_name,
            {
                'buy_options': selected_service
            },
            user_input
        )

        return self._handle_transaction_bet_number_request(session, selected_service)

    def _handle_transaction_phone_number_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
            """Request transaction confirmation - AUTOMATIC RESPONSE"""

            service_type = session.session_data.get('service_type', '')
            if session.current_screen == "AIRTIME_OPTIONS_BUY_ID":
                amount = 0
            else:
                amount = selected_option.get('amount', 0)

            data_volume = selected_option.get('name', '')

            confirmation_message = f"""{service_type}"""
# Amount: ₦{amount:,.2f}

            if data_volume:
                confirmation_message += f"\nData: {data_volume}"

            confirmation_message += """

Reply with phone number to proceed with the transaction"""

            WhatsAppSessionService.update_session_screen(
                session, session.current_screen,
                {
                    'selected_option': selected_option,
                    'amount': amount,
                    'user_phone_number': session.phone_number,
                }
            )

            return self._create_response(session, confirmation_message.strip(), session.current_screen)

    def _handle_transaction_cable_number_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
        """Request transaction confirmation - AUTOMATIC RESPONSE"""

        service_type = session.session_data.get('service_type', '')

        amount = selected_option.get('amount', 0)

        data_volume = selected_option.get('name', '')

        confirmation_message = f"""{service_type}
Amount: ₦{amount:,.2f}"""

        if data_volume:
            confirmation_message += f"\nData: {data_volume}"

        confirmation_message += """

Reply with IUC Number to proceed with the transaction"""

        WhatsAppSessionService.update_session_screen(
            session, session.current_screen,
            {
                'selected_option': selected_option,
                'amount': amount,
                'user_phone_number': session.phone_number,
            }
        )

        return self._create_response(session, confirmation_message.strip(), session.current_screen)

    def _handle_transaction_electric_number_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
        """Request transaction confirmation - AUTOMATIC RESPONSE"""

        service_type = session.session_data.get('service_type', '')
        if session.current_screen == "ELECTRIC_OPTIONS_BUY_ID":
            amount = 0
        else:
            amount = selected_option.get('amount', 0)

        data_volume = selected_option.get('name', '')

        confirmation_message = f"""{service_type}"""
# Amount: ₦{amount:,.2f}

        if data_volume:
            confirmation_message += f"\nData: {data_volume}"

        confirmation_message += """

Reply with Meter Number to proceed with the transaction"""

        WhatsAppSessionService.update_session_screen(
            session, session.current_screen,
            {
                'selected_option': selected_option,
                'amount': amount,
                'user_phone_number': session.phone_number,
            }
        )

        return self._create_response(session, confirmation_message.strip(), session.current_screen)

    def _handle_transaction_bet_number_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
            """Request transaction confirmation - AUTOMATIC RESPONSE"""

            service_type = session.session_data.get('service_type', '')
            if session.current_screen == "BET_OPTIONS_BUY_ID":
                amount = 0
            else:
                amount = selected_option.get('amount', 0)

            data_volume = selected_option.get('name', '')

            confirmation_message = f"""{service_type}"""
# Amount: ₦{amount:,.2f}

            if data_volume:
                confirmation_message += f"\nData: {data_volume}"

            confirmation_message += """

Reply with Bet ID to proceed with the transaction"""

            WhatsAppSessionService.update_session_screen(
                session, session.current_screen,
                {
                    'selected_option': selected_option,
                    'amount': amount,
                    'user_phone_number': session.phone_number,
                }
            )

            return self._create_response(session, confirmation_message.strip(), session.current_screen)

    def _handle_service_phone_number(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid phone number"
            )
            return self._create_response(session, message, session.current_screen)
        if len(user_input) != 11:
            message = WhatsAppResponseService.format_error_response(
                "Phone Number must be 11 digits"
            )
            return self._create_response(session, message, session.current_screen)
        selected_option = session.session_data.get('selected_option', [])
        
        WhatsAppSessionService.update_session_screen(
            session, session.current_screen, {
                'customer_id': user_input,
            }, user_input
        )
        message = "Please enter an amount to proceed with the transaction"
        if session.current_screen == "AIRTIME_OPTIONS_BUY_ID":
            screen_name = "AIRTIME_OPTIONS_BUY_ID_AMOUNT"
            WhatsAppSessionService.update_session_screen(
                session, screen_name,
                {
                }
            )
            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': screen_name,
            }
        if session.current_screen == "ELECTRIC_OPTIONS_BUY_ID":
            screen_name = "ELECTRIC_OPTIONS_BUY_ID_AMOUNT"
            WhatsAppSessionService.update_session_screen(
                session, screen_name,
                {
                }
            )
            return {
                'phone_number': session.phone_number,
                'message': message,
                'session_id': session.session_id,
                'screen': screen_name,
            }
            #return self._create_response(session, message, session.current_screen)

        return self._handle_transaction_confirmation_data_request(session, selected_option)
    
    def _handle_service_airtime_number(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid phone number"
            )
            return self._create_response(session, message, session.current_screen)
        if len(user_input) != 11:
            message = WhatsAppResponseService.format_error_response(
                "Phone Number must be 11 digits"
            )
            return self._create_response(session, message, session.current_screen)
        
        WhatsAppSessionService.update_session_screen(
            session, session.current_screen, {
                'customer_id': user_input,
            }, user_input
        )
        message = "Please enter an amount to proceed with the transaction"
        screen_name = "AIRTIME_OPTIONS_BUY_ID_AMOUNT"
        WhatsAppSessionService.update_session_screen(
            session, screen_name,
            {
            }
        )
        return {
            'phone_number': session.phone_number,
            'message': message,
            'session_id': session.session_id,
            'screen': screen_name,
        }
    
    def _handle_service_data_number(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid phone number"
            )
            return self._create_response(session, message, session.current_screen)
        if len(user_input) != 11:
            message = WhatsAppResponseService.format_error_response(
                "Phone Number must be 11 digits"
            )
            return self._create_response(session, message, session.current_screen)
        selected_option = session.session_data.get('selected_option', [])
        
        WhatsAppSessionService.update_session_screen(
            session, session.current_screen, {
                'customer_id': user_input,
            }, user_input
        )
        return self._handle_transaction_confirmation_data_request(session, selected_option)

    def _handle_service_cable_number(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a correct IUC number"
            )
            return self._create_response(session, message, session.current_screen)
        slug = session.session_data.get('selected_service', {}).get('slug', '')
        product_name = session.session_data.get('selected_option', {}).get("slug")
        customer_data = CoralPayApi.verify_cable_customer(slug, user_input, product_name)
        if customer_data is None:
            message = WhatsAppResponseService.format_error_response(
                f"Cannot Get user data with {user_input}, enter a correct IUC number"
            )
            return self._create_response(session, message, session.current_screen)

        customer_name = customer_data.get("data", {}).get("billerData", {}).get("customerName")
        selected_option = session.session_data.get('selected_option', [])
       
        WhatsAppSessionService.update_session_screen(
            session, session.current_screen, {
                'customer_id': user_input,
                'customer_name': customer_name
            }, user_input
        )
        return self._handle_transaction_confirmation_cable_request(session, selected_option)
    
    def _handle_service_bet_number(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        # Validate input

        slug = session.session_data.get('selected_service', {}).get('slug', '')
        product_name = session.session_data.get('selected_option', {}).get("slug")
        customer_data = CoralPayApi.verify_bet_customer(slug, user_input, product_name)
        if customer_data is None:
            message = WhatsAppResponseService.format_error_response(
                f"Cannot Get user data with {user_input}, enter a correct {product_name} Bet ID"
            )
            return self._create_response(session, message, session.current_screen)

        customer_name = customer_data.get("data", {}).get("billerData", {}).get("customerName")
      
        WhatsAppSessionService.update_session_screen(
            session, session.current_screen, {
                'customer_id': user_input,
                'customer_name': customer_name
            }, user_input
        )
        message = "Please enter an amount to proceed with the transaction"
        screen_name = "BET_OPTIONS_BUY_ID_AMOUNT"
        WhatsAppSessionService.update_session_screen(
            session, screen_name,
            {
            }
        )
        return {
            'phone_number': session.phone_number,
            'message': message,
            'session_id': session.session_id,
            'screen': screen_name,
        }

    def _handle_service_electric_number(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle service selection from main menu - AUTOMATIC RESPONSE"""

        # Validate input
        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a correct Meter Number"
            )
            return self._create_response(session, message, session.current_screen)

        slug = session.session_data.get('selected_service', {}).get('slug', '')
        product_name = session.session_data.get('selected_option', {}).get("slug")
        customer_data = CoralPayApi.verify_electric_customer(slug, user_input, product_name)
        if customer_data is None:
            message = WhatsAppResponseService.format_error_response(
                f"Cannot Get user data with {user_input}, enter a correct Meter number"
            )
            return self._create_response(session, message, session.current_screen)
        
        customer_name = customer_data.get("data", {}).get("billerData", {}).get("customerName")

        WhatsAppSessionService.update_session_screen(
            session, session.current_screen, {
                'customer_id': user_input,
                'customer_name': customer_name
            }, user_input
        )
        message = "Please enter an amount to proceed with the transaction"
        screen_name = "ELECTRIC_OPTIONS_BUY_ID_AMOUNT"
        WhatsAppSessionService.update_session_screen(
            session, screen_name,
            {
            }
        )
        return {
            'phone_number': session.phone_number,
            'message': message,
            'session_id': session.session_id,
            'screen': screen_name,
        }


    def _handle_option_selection(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle option selection (airtime/data amounts) - AUTOMATIC RESPONSE"""

        if not user_input.isdigit():
            message = WhatsAppResponseService.format_error_response(
                "Please enter a valid number"
            )
            return self._create_response(session, message, session.current_screen)

        # Handle back option
        if user_input == '0':
            return self._show_main_menu(session)

        # Get selected option
        options = session.session_data.get('options', [])
        selected_option = next(
            (opt for opt in options if opt['key'] == user_input), None
        )

        if not selected_option:
            message = WhatsAppResponseService.format_error_response(
                "Invalid selection. Please choose from the available options."
            )
            return self._create_response(session, message, session.current_screen)

        # Record the screen interaction
        WhatsAppSessionService.update_session_screen(
            session, session.current_screen, {}, user_input
        )

        # Handle custom amount
        if selected_option.get('amount') == 'custom':
            return self._handle_custom_amount_request(session, selected_option)

        # Confirm transaction - AUTOMATIC RESPONSE
        return self._handle_transaction_confirmation_request(session, selected_option)

    def _handle_custom_amount_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
        """Handle custom amount input request - AUTOMATIC RESPONSE"""

        service_type = session.session_data.get('service_type', '')
        min_amount = 50
        max_amount = 10000

        # Customize amount ranges based on service type
        if service_type == 'ELECTRICITY':
            min_amount = 1000
            max_amount = 50000
        elif service_type == 'CABLE':
            min_amount = 500
            max_amount = 20000

        message = f"Enter custom amount (₦{min_amount:,} - ₦{max_amount:,}):"

        WhatsAppSessionService.update_session_screen(
            session, 'CUSTOM_AMOUNT',
            {
                'selected_option': selected_option,
                'min_amount': min_amount,
                'max_amount': max_amount
            }
        )

        return self._create_response(session, message, 'CUSTOM_AMOUNT')

    def _handle_custom_amount(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle custom amount input - AUTOMATIC RESPONSE"""

        try:
            amount = float(user_input)
            min_amount = session.session_data.get('min_amount', 50)
            max_amount = session.session_data.get('max_amount', 10000)

            if amount < min_amount or amount > max_amount:
                raise ValueError("Amount out of range")

        except ValueError:
            min_amount = session.session_data.get('min_amount', 50)
            max_amount = session.session_data.get('max_amount', 10000)
            message = WhatsAppResponseService.format_error_response(
                f"Please enter a valid amount between ₦{min_amount:,} and ₦{max_amount:,}"
            )
            return self._create_response(session, message, 'CUSTOM_AMOUNT')

        # Record user input
        WhatsAppSessionService.update_session_screen(
            session, 'CUSTOM_AMOUNT', {}, user_input
        )

        # Create custom option with entered amount
        custom_option = session.session_data['selected_option'].copy()
        custom_option['amount'] = amount

        return self._handle_transaction_confirmation_request(session, custom_option)

    def _handle_airtime_custom_amount(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle custom amount input - AUTOMATIC RESPONSE"""

        try:
            amount = float(user_input)
            min_amount = session.session_data.get('min_amount', 50)
            max_amount = session.session_data.get('max_amount', 10000)

            if amount < min_amount or amount > max_amount:
                raise ValueError("Amount out of range")

        except ValueError:
            min_amount = session.session_data.get('min_amount', 50)
            max_amount = session.session_data.get('max_amount', 10000)
            message = WhatsAppResponseService.format_error_response(
                f"Please enter a valid amount between ₦{min_amount:,} and ₦{max_amount:,}"
            )
            return self._create_response(session, message, 'CUSTOM_AMOUNT')

        # Record user input
        WhatsAppSessionService.update_session_screen(
            session, 'AIRTIME_OPTION_BUY_ID_AMOUNT', {
                'payable_amount': amount
            }, user_input
        )

        # Create custom option with entered amount
        custom_option = session.session_data['selected_option'].copy()
        custom_option['amount'] = amount

        return self._handle_transaction_confirmation_airtime_request(session, custom_option)

    def _handle_electric_custom_amount(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle custom amount input - AUTOMATIC RESPONSE"""

        try:
            amount = float(user_input)
            min_amount = session.session_data.get('min_amount', 1000)
            max_amount = session.session_data.get('max_amount', 50000)

            if amount < min_amount or amount > max_amount:
                raise ValueError("Amount out of range")

        except ValueError:
            min_amount = session.session_data.get('min_amount', 1000)
            max_amount = session.session_data.get('max_amount', 50000)
            message = WhatsAppResponseService.format_error_response(
                f"Please enter a valid amount between ₦{min_amount:,} and ₦{max_amount:,}"
            )
            return self._create_response(session, message, 'CUSTOM_AMOUNT')

        # Record user input
        WhatsAppSessionService.update_session_screen(
            session, 'ELECTRIC_OPTION_BUY_ID_AMOUNT', {
                'payable_amount': amount
            }, user_input
        )

        # Create custom option with entered amount
        custom_option = session.session_data['selected_option'].copy()
        custom_option['amount'] = amount

        return self._handle_transaction_confirmation_electric_request(session, custom_option)
    
    def _handle_bet_custom_amount(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle custom amount input - AUTOMATIC RESPONSE"""

        try:
            amount = float(user_input)
            min_amount = session.session_data.get('min_amount', 500)
            max_amount = session.session_data.get('max_amount', 50000)

            if amount < min_amount or amount > max_amount:
                raise ValueError("Amount out of range")

        except ValueError:
            min_amount = session.session_data.get('min_amount', 500)
            max_amount = session.session_data.get('max_amount', 50000)
            message = WhatsAppResponseService.format_error_response(
                f"Please enter a valid amount between ₦{min_amount:,} and ₦{max_amount:,}"
            )
            return self._create_response(session, message, 'CUSTOM_AMOUNT')

        # Record user input
        WhatsAppSessionService.update_session_screen(
            session, 'BET_OPTION_BUY_ID_AMOUNT', {
                'payable_amount': amount
            }, user_input
        )

        # Create custom option with entered amount
        custom_option = session.session_data['selected_option'].copy()
        custom_option['amount'] = amount

        return self._handle_transaction_confirmation_bet_request(session, custom_option)

    def _handle_transaction_confirmation_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
        """Request transaction confirmation - AUTOMATIC RESPONSE"""

        service_type = session.session_data.get('service_type', '')
        amount = selected_option.get('amount', 0)
        data_volume = selected_option.get('name', '')
        customer_id = session.session_data.get('customer_id')
        confirmation_message = f"""Confirm Transaction:

Service: {service_type}
Amount: ₦{amount:,.2f}
Phone: {customer_id}"""

        if data_volume:
            confirmation_message += f"\nData: {data_volume}"

        confirmation_message += """

1. Confirm
2. Cancel"""

        WhatsAppSessionService.update_session_screen(
            session, 'CONFIRM_TRANSACTION',
            {
                'selected_option': selected_option,
                'amount': amount
            }
        )

        return self._create_response(session, confirmation_message.strip(), 'CONFIRM_TRANSACTION')
    
    def _handle_transaction_confirmation_airtime_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
        """Request transaction confirmation - AUTOMATIC RESPONSE"""

        service_type = session.session_data.get('service_type', '')
        amount = selected_option.get('amount', 0)
        data_volume = selected_option.get('name', '')
        customer_id = session.session_data.get('customer_id')
        confirmation_message = f"""Confirm Transaction:

Service: {service_type}
Amount: ₦{amount:,.2f}
Phone: {customer_id}"""

        if data_volume:
            confirmation_message += f"\nData: {data_volume}"

        confirmation_message += """

1. Confirm
2. Cancel"""

        WhatsAppSessionService.update_session_screen(
            session, 'CONFIRM_TRANSACTION_AIRTIME',
            {
                'selected_option': selected_option,
                'amount': amount
            }
        )

        return self._create_response(session, confirmation_message.strip(), 'CONFIRM_TRANSACTION_AIRTIME')
    
    def _handle_transaction_confirmation_data_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
        """Request transaction confirmation - AUTOMATIC RESPONSE"""

        service_type = session.session_data.get('service_type', '')
        amount = selected_option.get('amount', 0)
        data_volume = selected_option.get('name', '')
        customer_id = session.session_data.get('customer_id')
        confirmation_message = f"""Confirm Transaction:

Service: {service_type}
Amount: ₦{amount:,.2f}
Phone: {customer_id}"""

        if data_volume:
            confirmation_message += f"\nData: {data_volume}"

        confirmation_message += """

1. Confirm
2. Cancel"""

        WhatsAppSessionService.update_session_screen(
            session, 'CONFIRM_TRANSACTION_DATA',
            {
                'selected_option': selected_option,
                'amount': amount
            }
        )

        return self._create_response(session, confirmation_message.strip(), 'CONFIRM_TRANSACTION_DATA')

    def _handle_transaction_confirmation_electric_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
        """Request transaction confirmation - AUTOMATIC RESPONSE"""

        service_type = session.session_data.get('service_type', '')
        amount = selected_option.get('amount', 0)
        data_volume = selected_option.get('name', '')
        customer_id = session.session_data.get('customer_id')
        customer_name = session.session_data.get('customer_name')
        confirmation_message = f"""Confirm Transaction:

Service: {service_type}
Amount: ₦{amount:,.2f}
Meter Number: {customer_id}
Customer Name: {customer_name}"""

        if data_volume:
            confirmation_message += f"\nData: {data_volume}"

        confirmation_message += """

1. Confirm
2. Cancel"""

        WhatsAppSessionService.update_session_screen(
            session, 'CONFIRM_TRANSACTION_ELECTRIC',
            {
                'selected_option': selected_option,
                'amount': amount
            }
        )

        return self._create_response(session, confirmation_message.strip(), 'CONFIRM_TRANSACTION_ELECTRIC')

    def _handle_transaction_confirmation_cable_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
        """Request transaction confirmation - AUTOMATIC RESPONSE"""

        service_type = session.session_data.get('service_type', '')
        amount = selected_option.get('amount', 0)
        data_volume = selected_option.get('name', '')
        customer_id = session.session_data.get('customer_id')
        customer_name = session.session_data.get('customer_name')
        confirmation_message = f"""Confirm Transaction:

Service: {service_type}
Amount: ₦{amount:,.2f}
IUC Number: {customer_id}
Customer Name: {customer_name}"""

        if data_volume:
            confirmation_message += f"\nData: {data_volume}"

        confirmation_message += """

1. Confirm
2. Cancel"""

        WhatsAppSessionService.update_session_screen(
            session, 'CONFIRM_TRANSACTION_CABLE',
            {
                'selected_option': selected_option,
                'amount': amount
            }
        )

        return self._create_response(session, confirmation_message.strip(), 'CONFIRM_TRANSACTION_CABLE')
    
    def _handle_transaction_confirmation_bet_request(self, session: WhatsAppSession, selected_option: dict) -> dict:
        """Request transaction confirmation - AUTOMATIC RESPONSE"""

        service_type = session.session_data.get('service_type', '')
        amount = selected_option.get('amount', 0)
        data_volume = selected_option.get('name', '')
        customer_id = session.session_data.get('customer_id')
        customer_name = session.session_data.get('customer_name')
        confirmation_message = f"""Confirm Transaction:

Service: {service_type}
Amount: ₦{amount:,.2f}
Bet ID: {customer_id}
Customer Name: {customer_name}"""

        if data_volume:
            confirmation_message += f"\nData: {data_volume}"

        confirmation_message += """

1. Confirm
2. Cancel"""

        WhatsAppSessionService.update_session_screen(
            session, 'CONFIRM_TRANSACTION_BET',
            {
                'selected_option': selected_option,
                'amount': amount
            }
        )

        return self._create_response(session, confirmation_message.strip(), 'CONFIRM_TRANSACTION_BET')

    def _handle_transaction_confirmation(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle transaction confirmation - AUTOMATIC RESPONSE"""

        if user_input == '1':
            # Record confirmation input
            WhatsAppSessionService.update_session_screen(
                session, 'CONFIRM_TRANSACTION', {}, user_input
            )

            # Process transaction
            service_type = session.session_data.get('service_type', '')
            selected_option = session.session_data.get('selected_option', {})

            transaction_result = WhatsAppMenuService.process_transaction(
                service_type, user_input, session.phone_number, session.session_data
            )
            amount = session.session_data.get('amount')
            customer_id = session.session_data.get('customer_id')
            service_type = session.session_data.get('service_type')
            package_slug = session.session_data.get('selected_option').slug("slug")
            

            # buy_utility_bills(
            #     user_id: str, amount: float, provider: BillProvider, 
            #     customer_id: str, package_slug: str, customer_name: str, 
            #     phone_number: str, bills_type: BillsType, biller: str
            # )
            # Create transaction record
            WhatsAppTransaction.objects.create(
                session=session,
                transaction_type=service_type,
                amount=session.session_data.get('amount', 0),
                recipient=session.phone_number,
                status='SUCCESS' if transaction_result['status'] == 'success' else 'FAILED',
                transaction_ref=transaction_result.get('transaction_ref'),
                response_data=transaction_result
            )

            # Mark session as completed
            session.status = 'COMPLETED'
            session.save()

            message = transaction_result['message']
            return self._create_response(session, message, 'COMPLETED')

        elif user_input == '2':
            # Record cancellation
            WhatsAppSessionService.update_session_screen(
                session, 'CONFIRM_TRANSACTION', {}, user_input
            )

            # Cancel transaction - go back to main menu - AUTOMATIC RESPONSE
            return self._show_main_menu(session)

        else:
            message = WhatsAppResponseService.format_error_response(
                "Please enter 1 to confirm or 2 to cancel"
            )
            return self._create_response(session, message, 'CONFIRM_TRANSACTION')
        
    def _handle_transaction_confirmation_data(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle transaction confirmation - AUTOMATIC RESPONSE"""

        if user_input == '1':
            # Record confirmation input
            WhatsAppSessionService.update_session_screen(
                session, 'CONFIRM_TRANSACTION_DATA', {}, user_input
            )

            # Process transaction
            user_code = session.user_code
            amount = session.session_data.get('amount')
            customer_id = session.session_data.get('customer_id')
            package_slug = session.session_data.get('selected_option').get("slug")
            bills_type = "DATA_BUNDLE" 
            biller = session.session_data.get('product_name')
            if biller in ["MTN", "AIRTEL", "GLO", "9MOBILE"]:
                biller = f"{biller}_DATA"
            else:
                biller = biller

            formatted_customer_id = Profile.format_number_from_back_234(customer_id)
            message = VASTransaction.initiate_whatsapp_transaction(
                customer_id=formatted_customer_id,
                amount=amount,
                package_slug=package_slug,
                bills_type=bills_type,
                biller=biller,
                phone_number=session.phone_number,
                user_code=user_code,
                session=session
            )
        
            # Mark session as completed
            session.status = 'EXPIRED'
            session.save()
            return self._create_response(session, message, 'COMPLETED')

        elif user_input == '2':
            # Record cancellation
            WhatsAppSessionService.update_session_screen(
                session, 'CANCELLED', {}, user_input
            )
            # Cancel transaction - go back to main menu - AUTOMATIC RESPONSE
            # return self._show_main_menu(session)
            message = "Session Cancelled. ❌\n\nThank you for using VendBox!"
            session.status = 'EXPIRED'
            session.save()
            return self._create_response(session, message, 'CANCELLED')

        else:
            message = WhatsAppResponseService.format_error_response(
                "Please enter 1 to confirm or 2 to cancel"
            )
            return self._create_response(session, message, 'CONFIRM_TRANSACTION_DATA')
        
    def _handle_transaction_confirmation_airtime(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle transaction confirmation - AUTOMATIC RESPONSE"""

        if user_input == '1':
            # Record confirmation input
            WhatsAppSessionService.update_session_screen(
                session, 'CONFIRM_TRANSACTION_AIRTIME', {}, user_input
            )

            # Process transaction
            user_code = session.user_code
            amount = session.session_data.get('amount')
            customer_id = session.session_data.get('customer_id')
            package_slug = session.session_data.get('selected_option').get("slug")
            bills_type = "VTU" 

            biller = session.session_data.get('product_name')
            if biller in ["MTN", "AIRTEL", "GLO", "9MOBILE"]:
                biller = f"{biller}_VTU"
            else:
                biller = biller
            
            formatted_customer_id = Profile.format_number_from_back_234(customer_id)
            message = VASTransaction.initiate_whatsapp_transaction(
                customer_id=formatted_customer_id,
                amount=amount,
                package_slug=package_slug,
                bills_type=bills_type,
                biller=biller,
                phone_number=session.phone_number,
                user_code=user_code,
                session=session
            )
                    
            # Mark session as completed
            session.status = 'EXPIRED'
            session.save()
            return self._create_response(session, message, 'COMPLETED')

        elif user_input == '2':
            # Record cancellation
            WhatsAppSessionService.update_session_screen(
                session, 'CANCELLED', {}, user_input
            )
            # Cancel transaction - go back to main menu - AUTOMATIC RESPONSE
            # return self._show_main_menu(session)
            message = "Session Cancelled. ❌\n\nThank you for using VendBox!"
            session.status = 'EXPIRED'
            session.save()
            return self._create_response(session, message, 'CANCELLED')

        else:
            message = WhatsAppResponseService.format_error_response(
                "Please enter 1 to confirm or 2 to cancel"
            )
            return self._create_response(session, message, 'CONFIRM_TRANSACTION_AIRTIME')
        
    def _handle_transaction_confirmation_electric(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle transaction confirmation - AUTOMATIC RESPONSE"""

        if user_input == '1':
            # Record confirmation input
            WhatsAppSessionService.update_session_screen(
                session, 'CONFIRM_TRANSACTION_ELECTRIC', {}, user_input
            )

            # Process transaction
            user_code = session.user_code
            amount = session.session_data.get('amount')
            customer_id = session.session_data.get('customer_id')
            package_slug = session.session_data.get('selected_option').get("slug")
            bills_type = "ELECTRICITY" 
            biller = session.session_data.get('product_name')

            message = VASTransaction.initiate_whatsapp_transaction(
                customer_id=customer_id,
                amount=amount,
                package_slug=package_slug,
                bills_type=bills_type,
                biller=biller,
                phone_number=session.phone_number,
                user_code=user_code,
                session=session
            )
        
            # Mark session as completed
            session.status = 'EXPIRED'
            session.save()
            return self._create_response(session, message, 'COMPLETED')

        elif user_input == '2':
            # Record cancellation
            WhatsAppSessionService.update_session_screen(
                session, 'CANCELLED', {}, user_input
            )
            # Cancel transaction - go back to main menu - AUTOMATIC RESPONSE
            # return self._show_main_menu(session)
            message = "Session Cancelled. ❌\n\nThank you for using VendBox!"
            session.status = 'EXPIRED'
            session.save()
            return self._create_response(session, message, 'CANCELLED')

        else:
            message = WhatsAppResponseService.format_error_response(
                "Please enter 1 to confirm or 2 to cancel"
            )
            return self._create_response(session, message, 'CONFIRM_TRANSACTION_ELECTRIC')
        
    def _handle_transaction_confirmation_cable(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle transaction confirmation - AUTOMATIC RESPONSE"""

        if user_input == '1':
            # Record confirmation input
            WhatsAppSessionService.update_session_screen(
                session, 'CONFIRM_TRANSACTION_CABLE', {}, user_input
            )

            # Process transaction
            user_code = session.user_code
            amount = session.session_data.get('amount')
            customer_id = session.session_data.get('customer_id')
            package_slug = session.session_data.get('selected_option').get("slug")
            bills_type = "CABLE_TV" 
            biller = session.session_data.get('product_name')
            
            message = VASTransaction.initiate_whatsapp_transaction(
                customer_id=customer_id,
                amount=amount,
                package_slug=package_slug,
                bills_type=bills_type,
                biller=biller,
                phone_number=session.phone_number,
                user_code=user_code,
                session=session
            )
                    
            # Mark session as completed
            session.status = 'EXPIRED'
            session.save()
            return self._create_response(session, message, 'COMPLETED')

        elif user_input == '2':
            # Record cancellation
            WhatsAppSessionService.update_session_screen(
                session, 'CANCELLED', {}, user_input
            )
            # Cancel transaction - go back to main menu - AUTOMATIC RESPONSE
            # return self._show_main_menu(session)
            message = "Session Cancelled. ❌\n\nThank you for using VendBox!"
            session.status = 'EXPIRED'
            session.save()
            return self._create_response(session, message, 'CANCELLED')

        else:
            message = WhatsAppResponseService.format_error_response(
                "Please enter 1 to confirm or 2 to cancel"
            )
            return self._create_response(session, message, 'CONFIRM_TRANSACTION_CABLE')
        
    def _handle_transaction_confirmation_bet(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle transaction confirmation - AUTOMATIC RESPONSE"""

        if user_input == '1':
            # Record confirmation input
            WhatsAppSessionService.update_session_screen(
                session, 'CONFIRM_TRANSACTION_BET', {}, user_input
            )

            # Process transaction
            user_code = session.user_code
            amount = session.session_data.get('amount')
            customer_id = session.session_data.get('customer_id')
            package_slug = session.session_data.get('selected_option').get("slug")
            bills_type = "BETTING" 
            biller = session.session_data.get('product_name')
            
            message = VASTransaction.initiate_whatsapp_transaction(
                customer_id=customer_id,
                amount=amount,
                package_slug=package_slug,
                bills_type=bills_type,
                biller=biller,
                phone_number=session.phone_number,
                user_code=user_code,
                session=session
            )
                    
            # Mark session as completed
            session.status = 'EXPIRED'
            session.save()
            return self._create_response(session, message, 'COMPLETED')

        elif user_input == '2':
            # Record cancellation
            WhatsAppSessionService.update_session_screen(
                session, 'CANCELLED', {}, user_input
            )
            # Cancel transaction - go back to main menu - AUTOMATIC RESPONSE
            # return self._show_main_menu(session)
            message = "Session Cancelled. ❌\n\nThank you for using VendBox!"
            session.status = 'EXPIRED'
            session.save()
            return self._create_response(session, message, 'CANCELLED')

        else:
            message = WhatsAppResponseService.format_error_response(
                "Please enter 1 to confirm or 2 to cancel"
            )
            return self._create_response(session, message, 'CONFIRM_TRANSACTION_BET')

    def _handle_balance_check(self, session: WhatsAppSession, user_input: str) -> dict:
        """Handle balance check request - AUTOMATIC RESPONSE"""

        # Record the balance check request
        WhatsAppSessionService.update_session_screen(
            session, 'BALANCE_CHECK', {}, user_input
        )

        # Get balance from service (replace with actual service call)
        balance_result = UserRegistration.fetch_merchant_wallet_balance(phone_number=session.phone_number)

        wallet_status = balance_result.get("status", False)
        general_balance = balance_result.get("general_balance")
        commissions_balance = balance_result.get("commissions_balance")

        if wallet_status is True:
            general_balance = balance_result.get("general_balance")
            commissions_balance = balance_result.get("commissions_balance")

            message = f"""Account Balance

Main Balance: ₦{general_balance:,.2f}
Commissions Balance: ₦{commissions_balance:,.2f}

Thank you for using VendBox!"""
        else:
            message = WhatsAppResponseService.format_error_response(
                "Unable to retrieve balance at this time. Please try again."
            )

        # Mark session as completed after balance check
        session.status = 'EXPIRED'
        session.save()

        return self._create_response(session, message.strip(), 'COMPLETED')
    

    def _create_response(self, session: WhatsAppSession, message: str,
                         screen: str = None, options: list = None) -> dict:
        """Helper method to create standardized response"""
        return {
            'phone_number': session.phone_number,
            'message': message,
            'session_id': session.session_id,
            'screen': screen or session.current_screen,
            'options': options or []
        }


class WhatsAppSessionDetailAPIView(APIView):
    """
    API View for retrieving session details
    """
    permission_classes = [AllowAny]  # Adjust based on your requirements

    def get(self, request, session_id, *args, **kwargs):
        """Get session details by session ID"""
        try:
            session = get_object_or_404(WhatsAppSession, session_id=session_id)
            serializer = WhatsAppSessionSerializer(session)

            # Include related screens and transactions
            screens = WhatsAppScreen.objects.filter(session=session).order_by('-created_at')[:10]
            transactions = WhatsAppTransaction.objects.filter(session=session)

            response_data = serializer.data
            response_data['recent_screens'] = [
                {
                    'screen_name': screen.screen_name,
                    'user_input': screen.user_input,
                    'created_at': screen.created_at
                }
                for screen in screens
            ]
            response_data['transactions'] = [
                {
                    'transaction_type': trans.transaction_type,
                    'amount': trans.amount,
                    'status': trans.status,
                    'transaction_ref': trans.transaction_ref,
                    'created_at': trans.created_at
                }
                for trans in transactions
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error retrieving session {session_id}: {str(e)}")
            return Response(
                {"error": "Session not found or error occurred"},
                status=status.HTTP_404_NOT_FOUND
            )


class WhatsAppSessionListAPIView(APIView):
    """
    API View for listing sessions with filters
    """
    permission_classes = [AllowAny]  # Adjust based on your requirements

    def get(self, request, *args, **kwargs):
        """Get sessions list with optional filters"""
        try:
            queryset = WhatsAppSession.objects.all()

            # Apply filters
            phone_number = request.query_params.get('phone_number')
            if phone_number:
                queryset = queryset.filter(phone_number=phone_number)

            session_status = request.query_params.get('status')
            if session_status:
                queryset = queryset.filter(status=session_status)

            # Date range filter
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')
            if start_date:
                queryset = queryset.filter(created_at__gte=start_date)
            if end_date:
                queryset = queryset.filter(created_at__lte=end_date)

            # Order by latest
            queryset = queryset.order_by('-created_at')

            # Paginate (simple limit)
            limit = min(int(request.query_params.get('limit', 20)), 100)
            queryset = queryset[:limit]

            serializer = WhatsAppSessionSerializer(queryset, many=True)

            return Response({
                'sessions': serializer.data,
                'count': len(serializer.data)
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error listing sessions: {str(e)}")
            return Response(
                {"error": "Error retrieving sessions"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class AirtimeProviderAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        response = CoralPayApi.get_biller_service("AIRTIME_AND_DATA")
        return Response(
            response,
            status=status.HTTP_200_OK
        )
    
class DataProviderAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        response = CoralPayApi.get_biller_service("AIRTIME_AND_DATA")
        return Response(
            response,
            status=status.HTTP_200_OK
        )
    
class CableProviderAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        response = CoralPayApi.get_biller_service("PAY_TV")
        return Response(
            response,
            status=status.HTTP_200_OK
        )
    
class ElectricProviderAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        response = CoralPayApi.get_biller_service("ELECTRIC_DISCO")
        return Response(
            response,
            status=status.HTTP_200_OK
        )
    
class BetProviderAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        response = CoralPayApi.get_biller_service("BETTING_AND_LOTTERY")
        return Response(
            response,
            status=status.HTTP_200_OK
        )
    
class AirtimeListAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        provider_name = request.query_params.get("provider_name")
        response = CoralPayApi.get_biller_airtime_options(provider_name)
        return Response(
            response,
            status=status.HTTP_200_OK
        )
    
class DataListAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        provider_name = request.query_params.get("provider_name")
        response = CoralPayApi.get_biller_data_options(provider_name)
        return Response(
            response,
            status=status.HTTP_200_OK
        )
    
class CableListAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        provider_name = request.query_params.get("provider_name")
        response = CoralPayApi.get_biller_cable_options(provider_name)
        return Response(
            response,
            status=status.HTTP_200_OK
        )
    
class ElectricListAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        provider_name = request.query_params.get("provider_name")
        response = CoralPayApi.get_biller_electric_options(provider_name)
        return Response(
            response,
            status=status.HTTP_200_OK
        )
    
class BetListAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        provider_name = request.query_params.get("provider_name")
        response = CoralPayApi.get_biller_bet_options(provider_name)
        return Response(
            response,
            status=status.HTTP_200_OK
        )
    
class BillPaymentAPIView(APIView):
    permission_classes = [AllowAny]
    # permission_classes = [BlockVPNProxyMiddleware]

    def post(self, request):
        serializer = BillPaymentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        merchant_code = serializer.validated_data.get("merchant_code")
        customer_id = serializer.validated_data.get("customer_id")
        amount = serializer.validated_data.get("amount")
        package_slug = serializer.validated_data.get("package_slug")
        phone_number = serializer.validated_data.get("phone_number")
        bills_type = serializer.validated_data.get("bills_type")
        provider = serializer.validated_data.get("provider")

        if bills_type in ["DATA_BUNDLE", "VTU"]:
            this_biller = provider.split("_")[0]
            if bills_type == "VTU":
                biller = f"{this_biller}_VTU"
                package_slug = biller
            elif bills_type == "DATA_BUNDLE":
                biller = f"{this_biller}_DATA"
                package_slug = package_slug
        else:
            biller = provider
            package_slug = package_slug
        
        response = VASTransaction.initiate_web_transaction(
            customer_id=customer_id,
            amount=amount,
            package_slug=package_slug,
            bills_type=bills_type,
            biller=biller,
            phone_number=phone_number,
            user_code=merchant_code,
        )
        if response.get("status"):
            return Response(
                response,
                status=status.HTTP_200_OK
            )
        else:
            return Response(
                response,
                status=status.HTTP_400_BAD_REQUEST
            )


class BillPaymentSesionAPIView(APIView):
    # permission_classes = [IsAuthenticated, IsMerchantActivated]
    permission_classes = [IsAuthenticated]

    def post(self, request):

        phone_number = request.user.phone
        serializer = BillPaymentSessionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        merchant_code = serializer.validated_data.get("merchant_code")
        customer_id = serializer.validated_data.get("customer_id")
        amount = serializer.validated_data.get("amount")
        package_slug = serializer.validated_data.get("package_slug")
        bills_type = serializer.validated_data.get("bills_type")
        provider = serializer.validated_data.get("provider")

        if bills_type in ["DATA_BUNDLE", "VTU"]:
            this_biller = provider.split("_")[0]
            if bills_type == "VTU":
                biller = f"{this_biller}_VTU"
                package_slug = biller
            elif bills_type == "DATA_BUNDLE":
                biller = f"{this_biller}_DATA"
                package_slug = package_slug
        else:
            biller = provider
            package_slug = package_slug

        response = VASTransaction.initiate_web_session_transaction(
            customer_id=customer_id,
            amount=amount,
            package_slug=package_slug,
            bills_type=bills_type,
            biller=biller,
            phone_number=phone_number,
            user_code=merchant_code,
        )
        if response.get("status"):
            return Response(
                response,
                status=status.HTTP_200_OK
            )
        else:
            return Response(
                response,
                status=status.HTTP_400_BAD_REQUEST
            )
        

class TransactionHistoryAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = ListTransactionHistorySerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter)

    filterset_fields = (
        "status",
    )

    search_fields = (
        "transaction_ref",
        "vas_transaction_id",
    )

    def get_queryset(self):
        user = self.request.user

        transactions_history = Transaction.objects.filter(user=user)
        return transactions_history

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)