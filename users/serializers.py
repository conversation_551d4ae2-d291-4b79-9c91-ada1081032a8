from django.contrib.auth import get_user_model, authenticate
from django.utils import timezone
from rest_framework import serializers
from rest_framework.exceptions import APIException
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from users.models import Profile
User = get_user_model()
# Create your url pattern(s) here.

class CustomAuthenticationError(APIException):
    status_code = 400  # You can customize the status code
    default_detail = {"status_code": "103", "message": "Invalid username/password"}
    default_code = "authentication_failed"

    def __init__(self, detail=None, code=None):
        # Use the provided detail if present, otherwise fallback to default_detail
        if detail is None:
            detail = self.default_detail
        super().__init__(detail, code)

class CustomTokenObtainSerializer(TokenObtainPairSerializer):
    username_field = "phone"

    def validate(self, attrs):
        this_phone_number = attrs.get(self.username_field)
        password = attrs.get("password")
        if len(this_phone_number) != 11:
            raise CustomAuthenticationError(
                {"status_code": "101", "message": "phone_number must at 11 digits"}
            )
        
        attrs["phone_number"] = User.format_number_from_back_234(this_phone_number)
        phone_number = attrs.get("phone_number")

        """
        Checking if the user exists by getting the phonr_number(username field) from authentication_kwargs.
        If the user exists we check if the user account is active.
        If the user account is not active we raise the exception and pass the message.
        Thus stopping the user from getting authenticated altogether.

        And if the user does not exist at all we raise an exception with a different error message.
        Thus stopping the execution right there.
        """
        try:
            user = User.objects.get(phone=phone_number)
            if not user.password:
                return {"status_code": "102", "message": "User should reset password"}
            if not user.check_password(password):
                raise CustomAuthenticationError(
                    {"status_code": "103", "message": "Invalid phone_number/password"}
                )

        except User.DoesNotExist:
            raise CustomAuthenticationError(
                {"status_code": "103", "message": "Invalid phone_number/password"}
            )

        user = authenticate(phone=phone_number, password=password)
        if user is None:
            raise CustomAuthenticationError(
                {"status_code": "103", "message": "Invalid phone_number/password"}
            )
            # Create the token pair

        refresh = self.get_token(user)
        this_time = timezone.localtime()
        user.last_login = this_time
        user.save()

        # Add custom data to the token response if needed
        data = {
            "status_code": "100",
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            "phone": phone_number,
            "first_name": user.first_name,
            "last_name": user.last_name,
        }

        return data

    @classmethod
    def get_token(cls, user):
        # Create a refresh token
        token = RefreshToken.for_user(user)
        return token
    
class PhoneLoginCustomObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainSerializer

class UserRegistrationSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=255, required=True)
    first_name = serializers.CharField(max_length=255, required=True)
    last_name = serializers.CharField(max_length=255, required=True)
    password = serializers.CharField(max_length=255, required=True)
    store_name = serializers.CharField(max_length=255, required=True)
    email = serializers.EmailField()
    state = serializers.CharField(max_length=255, required=True)

    def validate(self, attrs):
        this_phone_number = attrs.get("phone_number")
        email = attrs.get("email")
        if len(this_phone_number) != 11:
            raise serializers.ValidationError({
                "phone_number" : "phone_number must at 11 digits"
            })
        
        attrs["phone_number"] = User.format_number_from_back_234(this_phone_number)
        phone_number = attrs.get("phone_number")

        if Profile.objects.filter(phone=phone_number).exists():
            raise serializers.ValidationError(
                {
                    "phone_number": "A user with this phone number already exists."
                }
            )
        if email:
            if Profile.objects.filter(email=email).exists():
                raise serializers.ValidationError(
                {
                    "email": "A user with this email already exists."
                }
            )
            attrs["email"] = email.lower()
            
        return attrs


