from django.urls import path, include
from users import views
from users.serializers import PhoneLoginCustomObtainPairView

# URLCOnf

user_service = [
    path("register_user/", views.RegistrationAPIView.as_view()),
    path("fetch_user_details/", views.FetchUserDetailsAPIView.as_view()),
    path("phone_signin/", PhoneLoginCustomObtainPairView.as_view()),
    path("biller_list/", views.BillerListAPIView.as_view()),
    # path("transaction_status/", views.BillerListAPIView.as_view()),
]

urlpatterns = [
    *user_service,
]