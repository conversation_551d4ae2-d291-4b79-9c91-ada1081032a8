# serializers.py
from rest_framework import serializers
from account.models import Transaction
from vendboss.models import VendboxUserProfile, GeneralAccount, AirtimeAccount, WhatsAppSession, WhatsAppScreen, WhatsAppTransaction
import re

from django.contrib.auth import get_user_model
User = get_user_model()

class VendboxProfileSerializer(serializers.ModelSerializer):
    """Serializer for creating Vendbox profiles"""

    class Meta:
        model = VendboxUserProfile
        fields = ['phone_number', 'name']

    # def generate_whatsapp_url(self, phone_number):
    #     """Generate WhatsApp URL from phone number"""
    #     # Clean phone number (remove non-digits)
    #     clean_phone = re.sub(r'\D', '', phone_number)
    #
    #     # Add country code if not present (assuming Nigeria +234)
    #     if not clean_phone.startswith('234'):
    #         if clean_phone.startswith('0'):
    #             clean_phone = '234' + clean_phone[1:]
    #         else:
    #             clean_phone = '234' + clean_phone
    #
    #     return f"https://wa.me/{clean_phone}"
    #
    # def generate_web_url(self, name):
    #     """Generate web URL from name"""
    #     # Convert name to lowercase and replace spaces with hyphens
    #     clean_name = re.sub(r'[^\w\s-]', '', name.lower())
    #     clean_name = re.sub(r'\s+', '-', clean_name)
    #     return f"https://vendbox.com/profile/{clean_name}"
    #
    # def create_default_accounts(self, validated_data):
    #     """Create default general and airtime accounts"""
    #     name = validated_data['name']
    #     phone_number = validated_data['phone_number']
    #
    #     # Create general account
    #     general_account = GeneralAccount.objects.create(
    #         name=f"{name} - General",
    #         phone_number=phone_number,
    #         account_number="**********",  # Default account number
    #         bank_name="Default Bank"  # Default bank name
    #     )
    #
    #     # Create airtime account
    #     airtime_account = AirtimeAccount.objects.create(
    #         name=f"{name} - Airtime",
    #         phone_number=phone_number,
    #         account_number="**********",  # Default account number
    #         bank_name="Default Bank"  # Default bank name
    #     )

        # return general_account, airtime_account

    def validate_phone_number(self, value):
        """Validate phone number format and uniqueness"""
        # Basic phone number validation
        clean_phone = re.sub(r'\D', '', value)
        if len(clean_phone) < 10:
            raise serializers.ValidationError("Phone number must be at least 10 digits")

        # Check if phone number already exists
        if VendboxUserProfile.objects.filter(phone_number=value).exists():
            raise serializers.ValidationError("Vendbox profile already exists")

        return value

    def validate_name(self, value):
        """Validate name field"""
        if len(value.strip()) < 2:
            raise serializers.ValidationError("Name must be at least 2 characters long")
        return value.strip()

    def create(self, validated_data):
        """Create Vendbox profile with accounts and URLs"""
        phone_number = validated_data['phone_number']
        name = validated_data['name']

        # Create default accounts
        general_account, airtime_account = self.create_default_accounts(validated_data)

        # Generate URLs
        whatsapp_url = self.generate_whatsapp_url(phone_number)
        web_url = self.generate_web_url(name)

        # Create Vendbox profile
        vendbox_profile = VendboxUserProfile.objects.create(
            phone_number=phone_number,
            name=name,
            whatsapp_url=whatsapp_url,
            web_url=web_url,
            general_account=general_account,
            airtime_account=airtime_account
        )

        return vendbox_profile

    def to_representation(self, instance):
        """Custom representation for response"""
        return {
            'id': str(instance.id),
            'phone_number': instance.phone_number,
            'name': instance.name,
            'whatsapp_url': instance.whatsapp_url,
            'web_url': instance.web_url,
            'general_account': {
                'id': str(instance.general_account.id),
                'name': instance.general_account.name,
                'phone_number': instance.general_account.phone_number,
                'account_number': instance.general_account.account_number,
                'bank_name': instance.general_account.bank_name
            },
            'airtime_account': {
                'id': str(instance.airtime_account.id),
                'name': instance.airtime_account.name,
                'phone_number': instance.airtime_account.phone_number,
                'account_number': instance.airtime_account.account_number,
                'bank_name': instance.airtime_account.bank_name
            },
            'created_at': instance.created_at,
            'updated_at': instance.updated_at
        }


class WhatsAppWebhookRequestSerializer(serializers.Serializer):
    """Enhanced serializer for incoming WhatsApp webhook requests with dynamic extraction"""
    object = serializers.CharField(required=False)
    entry = serializers.ListField(required=True)

    def validate(self, data):
        """Extract phone number, message, and metadata from WhatsApp webhook structure"""
        try:
            # Navigate the WhatsApp webhook structure
            entry = data['entry'][0]
            changes = entry['changes'][0]
            value = changes['value']

            # Check if this is a message update
            if changes.get('field') != 'messages':
                raise serializers.ValidationError("Not a message update")

            # Extract messages
            messages = value.get('messages', [])
            if not messages:
                raise serializers.ValidationError("No messages found")

            message_data = messages[0]

            # Extract phone number (from field)
            phone_number = message_data.get('from')
            if not phone_number:
                raise serializers.ValidationError("Phone number not found")

            # Extract message content
            message_content = None
            message_type = message_data.get('type')

            if message_type == 'text':
                message_content = message_data.get('text', {}).get('body', '').strip()
            else:
                raise serializers.ValidationError(f"Unsupported message type: {message_type}")

            if not message_content:
                raise serializers.ValidationError("Message content is empty")

            # Extract metadata dynamically
            metadata = value.get('metadata', {})
            phone_number_id = metadata.get('phone_number_id')
            display_phone_number = metadata.get('display_phone_number')

            # Validate phone number format
            cleaned_phone = ''.join(filter(str.isdigit, phone_number))
            if len(cleaned_phone) < 10 or len(cleaned_phone) > 15:
                raise serializers.ValidationError("Invalid phone number format")

            # Return the extracted data with dynamic metadata
            return {
                'phone_number': cleaned_phone,
                'message': message_content,
                'phone_number_id': phone_number_id,
                'display_phone_number': display_phone_number,
                'original_data': data
            }

        except (KeyError, IndexError, TypeError) as e:
            raise serializers.ValidationError(f"Invalid WhatsApp webhook structure: {str(e)}")


class WhatsAppWebhookResponseSerializer(serializers.Serializer):
    """Serializer for outgoing WhatsApp webhook responses"""
    phone_number = serializers.CharField()
    message = serializers.CharField()
    session_id = serializers.UUIDField(required=False)
    screen = serializers.CharField()
    options = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True
    )

class WhatsAppMessageResponseSerializer(serializers.Serializer):
    """Serializer for WhatsApp API message response format"""
    messaging_product = serializers.CharField(default="whatsapp")
    to = serializers.CharField()
    type = serializers.CharField(default="text")
    text = serializers.DictField()

    def to_whatsapp_format(self):
        """Convert to WhatsApp API format"""
        return {
            "messaging_product": self.validated_data["messaging_product"],
            "to": self.validated_data["to"],
            "type": self.validated_data["type"],
            "text": self.validated_data["text"]
        }

class WhatsAppSessionSerializer(serializers.ModelSerializer):
    """Serializer for WhatsApp sessions"""
    is_expired = serializers.SerializerMethodField()

    class Meta:
        model = WhatsAppSession
        fields = [
            'session_id', 'phone_number', 'six_digit_code', 'status',
            'current_screen', 'session_data', 'created_at', 'updated_at',
            'expires_at', 'is_expired'
        ]
        read_only_fields = ['session_id', 'created_at', 'updated_at']

    def get_is_expired(self, obj):
        return obj.is_expired()


class WhatsAppScreenSerializer(serializers.ModelSerializer):
    """Serializer for WhatsApp screens"""

    class Meta:
        model = WhatsAppScreen
        fields = [
            'id', 'screen_name', 'screen_data', 'user_input',
            'response_sent', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class WhatsAppTransactionSerializer(serializers.ModelSerializer):
    """Serializer for WhatsApp transactions"""

    class Meta:
        model = WhatsAppTransaction
        fields = [
            'id', 'transaction_type', 'amount', 'recipient', 'status',
            'transaction_ref', 'response_data', 'created_at', 'completed_at'
        ]
        read_only_fields = ['id', 'created_at', 'completed_at']


class BillPaymentSerializer(serializers.Serializer):
    BILLS_TYPE = [
        ("DATA_BUNDLE","DATA_BUNDLE"),
        ("VTU","VTU"),
        ("ELECTRICITY","ELECTRICITY"),
        ("CABLE_TV","CABLE_TV"),
        ("BETTING","BETTING")
    ]
    merchant_code = serializers.CharField(required=True, allow_blank=False, allow_null=True)
    customer_id = serializers.CharField(required=True)
    amount = serializers.FloatField(required=True)
    package_slug = serializers.CharField(required=True)
    phone_number = serializers.CharField(required=True)
    bills_type = serializers.ChoiceField(required=True, choices=BILLS_TYPE)
    provider = serializers.CharField(required=True)

    def validate(self, attrs):
        phone_number = attrs.get("phone_number")
        amount = attrs.get("amount")
        if len(phone_number) != 11:
            raise serializers.ValidationError({
                "phone_number" : "phone_number must at 11 digits"
            })
        if amount < 100:
            raise serializers.ValidationError({
                "amount" : "min amount is 100"
            })
         
        attrs["phone_number"] = User.format_number_from_back_234(phone_number)
        return attrs        
    
class BillPaymentSessionSerializer(serializers.Serializer):
    BILLS_TYPE = [
        ("DATA_BUNDLE","DATA_BUNDLE"),
        ("VTU","VTU"),
        ("ELECTRICITY","ELECTRICITY"),
        ("CABLE_TV","CABLE_TV"),
        ("BETTING","BETTING")
    ]
    merchant_code = serializers.CharField(required=True, allow_blank=False, allow_null=True)
    customer_id = serializers.CharField(required=True)
    amount = serializers.FloatField(required=True)
    package_slug = serializers.CharField(required=True)
    bills_type = serializers.ChoiceField(required=True, choices=BILLS_TYPE)
    provider = serializers.CharField(required=True)      

    def validate(self, attrs):
        amount = attrs.get("amount")
        if amount < 100:
            raise serializers.ValidationError({
                "amount" : "min amount is 100"
            })
        return attrs 
    

class ListTransactionHistorySerializer(serializers.ModelSerializer):

    class Meta:
        model = Transaction
        fields = (
            "id",
            "amount",
            "status",
            "debit_amount",
            "total_amount_received",
            "provider_fee",
            "stamp_duty",
            "transaction_type",
            "transaction_ref",
            "vas_transaction_id",
            "date_created",
            "last_updated",
            "narration",
        )