# admin.py
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse, NoReverseMatch
from vendboss.models import GeneralAccount, AirtimeAccount, VendboxUserProfile, WhatsAppSession, WhatsAppScreen, WhatsAppTransaction
import json
from django.utils.safestring import mark_safe



@admin.register(GeneralAccount)
class GeneralAccountAdmin(admin.ModelAdmin):
    """Admin configuration for GeneralAccount model"""

    # Display configuration
    list_display = [
        'name',
        'bank_name',
        'account_number',
        'phone_number',
        'profile_count',
        'created_at',
    ]

    list_filter = [
        'bank_name',
        'created_at',
        'updated_at',
    ]

    search_fields = [
        'name',
        'account_number',
        'phone_number',
        'bank_name'
    ]

    # Form configuration
    fields = [
        'name',
        'phone_number',
        'account_number',
        'bank_name',
    ]

    # Read-only fields (assuming BaseModel has these)
    readonly_fields = ['created_at', 'updated_at']

    # Ordering
    ordering = ['-created_at']

    # Pagination
    list_per_page = 25

    # Additional configurations
    list_select_related = True
    save_on_top = True

    def profile_count(self, obj):
        """Display count of related VendboxUserProfile instances"""
        count = obj.vendbox_profiles.count()
        if count > 0:
            url = reverse('admin:yourapp_vendboxuserprofile_changelist') + f'?general_account__id={obj.id}'
            return format_html('<a href="{}">{} profiles</a>', url, count)
        return "0 profiles"

    profile_count.short_description = "Linked Profiles"
    profile_count.admin_order_field = 'vendbox_profiles__count'

    def get_queryset(self, request):
        """Optimize queryset with annotations"""
        queryset = super().get_queryset(request)
        return queryset.prefetch_related('vendbox_profiles')


@admin.register(AirtimeAccount)
class AirtimeAccountAdmin(admin.ModelAdmin):
    """Admin configuration for AirtimeAccount model"""

    # Display configuration
    list_display = [
        'name',
        'bank_name',
        'account_number',
        'phone_number',
        'profile_count',
        'created_at',
    ]

    list_filter = [
        'bank_name',
        'created_at',
        'updated_at',
    ]

    search_fields = [
        'name',
        'account_number',
        'phone_number',
        'bank_name'
    ]

    # Form configuration
    fields = [
        'name',
        'phone_number',
        'account_number',
        'bank_name',
    ]

    # Read-only fields
    readonly_fields = ['created_at', 'updated_at']

    # Ordering
    ordering = ['-created_at']

    # Pagination
    list_per_page = 25

    # Additional configurations
    list_select_related = True
    save_on_top = True

    def profile_count(self, obj):
        """Display count of related VendboxUserProfile instances"""
        count = obj.vendbox_profiles.count()
        if count > 0:
            url = reverse('admin:yourapp_vendboxuserprofile_changelist') + f'?airtime_account__id={obj.id}'
            return format_html('<a href="{}">{} profiles</a>', url, count)
        return "0 profiles"

    profile_count.short_description = "Linked Profiles"
    profile_count.admin_order_field = 'vendbox_profiles__count'

    def get_queryset(self, request):
        """Optimize queryset with annotations"""
        queryset = super().get_queryset(request)
        return queryset.prefetch_related('vendbox_profiles')


@admin.register(VendboxUserProfile)
class VendboxUserProfileAdmin(admin.ModelAdmin):
    """Admin configuration for VendboxUserProfile model"""

    # Display configuration
    list_display = [
        'name',
        'phone_number',
        'general_account_link',
        'airtime_account_link',
        'whatsapp_link',
        'web_link',
        'created_at',
    ]

    list_filter = [
        'general_account__bank_name',
        'airtime_account__bank_name',
        'created_at',
        'updated_at',
    ]

    search_fields = [
        'name',
        'phone_number',
        'general_account__name',
        'airtime_account__name',
        'general_account__bank_name',
        'airtime_account__bank_name',
    ]

    # Form configuration
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'phone_number']
        }),
        ('Account Links', {
            'fields': ['general_account', 'airtime_account'],
            'classes': ['wide']
        }),
        ('External Links', {
            'fields': ['whatsapp_url', 'web_url'],
            'classes': ['collapse']
        }),
    ]

    # Read-only fields
    readonly_fields = ['created_at', 'updated_at']

    # Related fields configuration
    autocomplete_fields = ['general_account', 'airtime_account']

    # Ordering
    ordering = ['-created_at']

    # Pagination
    list_per_page = 25

    # Additional configurations
    list_select_related = ['general_account', 'airtime_account']
    save_on_top = True

    def general_account_link(self, obj):
        """Display clickable link to general account"""
        if obj.general_account:
            url = reverse('admin:yourapp_generalaccount_change', args=[obj.general_account.pk])
            return format_html('<a href="{}">{}</a>', url, obj.general_account.name)
        return "-"

    general_account_link.short_description = "General Account"
    general_account_link.admin_order_field = 'general_account__name'

    def airtime_account_link(self, obj):
        """Display clickable link to airtime account"""
        if obj.airtime_account:
            url = reverse('admin:yourapp_airtimeaccount_change', args=[obj.airtime_account.pk])
            return format_html('<a href="{}">{}</a>', url, obj.airtime_account.name)
        return "-"

    airtime_account_link.short_description = "Airtime Account"
    airtime_account_link.admin_order_field = 'airtime_account__name'

    def whatsapp_link(self, obj):
        """Display clickable WhatsApp link"""
        if obj.whatsapp_url:
            return format_html('<a href="{}" target="_blank">WhatsApp</a>', obj.whatsapp_url)
        return "-"

    whatsapp_link.short_description = "WhatsApp"

    def web_link(self, obj):
        """Display clickable web link"""
        if obj.web_url:
            return format_html('<a href="{}" target="_blank">Website</a>', obj.web_url)
        return "-"

    web_link.short_description = "Website"

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        queryset = super().get_queryset(request)
        return queryset.select_related('general_account', 'airtime_account')

class WhatsAppTransactionInline(admin.TabularInline):
    model = WhatsAppTransaction
    extra = 0
    readonly_fields = ['created_at', 'completed_at', 'transaction_ref']
    fields = ['transaction_type', 'amount', 'status', 'transaction_ref', 'created_at']

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(WhatsAppSession)
class WhatsAppSessionAdmin(admin.ModelAdmin):
    list_display = ['phone_number', 'user_code', 'status', 'current_screen', 'session_status_display',
                    'is_session_expired', 'created_at', 'expires_at']
    list_filter = ['status', 'current_screen', 'created_at']
    search_fields = ['phone_number', 'user_code', 'session_id']
    readonly_fields = ['session_id', 'created_at', 'updated_at', 'is_session_expired', 'session_status_display']

    fieldsets = (
        ('Session Info', {
            'fields': ('session_id', 'phone_number', 'user_code', 'status', 'current_screen')
        }),
        ('Data & Expiration', {
            'fields': ('session_data', 'expires_at', 'session_status_display')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def is_session_expired(self, obj):
        """Display if session is expired as a boolean"""
        return obj.is_expired()

    is_session_expired.short_description = 'Expired'
    is_session_expired.boolean = True  # This creates a green/red icon automatically

    def session_status_display(self, obj):
        """Display session status with color coding"""
        expired = obj.is_expired()
        if expired:
            return format_html(
                '<span style="color: #dc3545; font-weight: bold;">🔴 Expired</span>'
            )
        else:
            return format_html(
                '<span style="color: #28a745; font-weight: bold;">🟢 Active</span>'
            )

    session_status_display.short_description = 'Session Status'

    is_session_expired.short_description = 'Expired Status'
    is_session_expired.boolean = True


@admin.register(WhatsAppScreen)
class WhatsAppScreenAdmin(admin.ModelAdmin):
    list_display = ['get_session_link', 'screen_name', 'user_input_preview', 'created_at']
    list_filter = ['screen_name', 'created_at', 'session__status']
    search_fields = ['session__phone_number', 'screen_name', 'user_input', 'session__user_code']
    readonly_fields = ['created_at', 'updated_at', 'get_session_link', 'formatted_screen_data']
    raw_id_fields = ['session']  # This makes session selection more efficient

    fieldsets = (
        ('Screen Info', {
            'fields': ('get_session_link', 'screen_name', 'user_input', 'response_sent')
        }),
        ('Data', {
            'fields': ('formatted_screen_data',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_session_link(self, obj):
        """
        Create a clickable link to the related WhatsApp session.
        This method handles the URL reverse properly.
        """
        if not obj.session:
            return format_html('<em>No session</em>')

        try:
            # Get the app label from the session model
            app_label = obj.session._meta.app_label
            model_name = obj.session._meta.model_name

            # Construct the admin URL pattern
            url_pattern = f'admin:{app_label}_{model_name}_change'
            url = reverse(url_pattern, args=[obj.session.pk])

            return format_html(
                '<a href="{}" target="_blank" title="View Session Details">{} ({})</a>',
                url,
                obj.session.phone_number,
                obj.session.status
            )

        except NoReverseMatch as e:
            # Fallback: return non-clickable information
            return format_html(
                '<span title="Admin link not available: {}">{} ({})</span>',
                str(e),
                obj.session.phone_number,
                obj.session.status
            )
        except Exception as e:
            # Handle any other exceptions
            return format_html(
                '<span title="Error: {}">{}</span>',
                str(e),
                obj.session.phone_number if obj.session.phone_number else f"Session #{obj.session.pk}"
            )

    get_session_link.short_description = 'Session'
    get_session_link.admin_order_field = 'session__phone_number'  # Enable sorting

    def user_input_preview(self, obj):
        """Show a preview of user input"""
        if not obj.user_input:
            return format_html('<em>No input</em>')

        # Truncate long inputs
        preview = obj.user_input[:50]
        if len(obj.user_input) > 50:
            preview += "..."

        return format_html(
            '<span title="{}">{}</span>',
            obj.user_input,  # Full text in tooltip
            preview
        )

    user_input_preview.short_description = 'User Input'

    def formatted_screen_data(self, obj):
        """
        Display formatted JSON data for the screen.
        """
        if not obj.screen_data:
            return mark_safe('<em style="color: #999;">No screen data</em>')

        try:
            # Pretty print the JSON with syntax highlighting
            formatted_json = json.dumps(obj.screen_data, indent=2, ensure_ascii=False, sort_keys=True)
            return format_html(
                '''
                <div style="max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                    <pre style="margin: 0; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 12px;">{}</pre>
                </div>
                ''',
                formatted_json
            )
        except (TypeError, ValueError) as e:
            return format_html(
                '<div style="color: #dc3545; padding: 10px; background: #f8d7da; border-radius: 4px;">'
                '<strong>Error formatting JSON:</strong><br>'
                '<code>{}</code><br><br>'
                '<strong>Raw data:</strong><br>'
                '<pre>{}</pre>'
                '</div>',
                str(e),
                str(obj.screen_data)
            )

    formatted_screen_data.short_description = 'Screen Data'

    def get_queryset(self, request):
        """Optimize queries by selecting related session data"""
        return super().get_queryset(request).select_related('session')


# Optional: Inline admin to show screens within session admin
class WhatsAppScreenInline(admin.TabularInline):
    model = WhatsAppScreen
    extra = 0
    readonly_fields = ['created_at', 'screen_name', 'user_input_preview']
    fields = ['screen_name', 'user_input_preview', 'created_at']

    def user_input_preview(self, obj):
        if not obj.user_input:
            return "-"
        return obj.user_input[:30] + "..." if len(obj.user_input) > 30 else obj.user_input

    user_input_preview.short_description = 'User Input'

    def has_add_permission(self, request, obj=None):
        return False  # Prevent adding screens directly from session admin


# Update the WhatsAppSessionAdmin to include the inline
# Add this to the WhatsAppSessionAdmin class:
# inlines = [WhatsAppScreenInline]

@admin.register(WhatsAppTransaction)
class WhatsAppTransactionAdmin(admin.ModelAdmin):
    list_display = [
        'transaction_ref', 'session_link', 'transaction_type',
        'amount', 'status', 'created_at'
    ]
    list_filter = ['transaction_type', 'status', 'created_at']
    search_fields = [
        'transaction_ref', 'session__phone_number',
        'transaction_type', 'recipient'
    ]
    readonly_fields = [
        'created_at', 'completed_at', 'session_link',
        'formatted_response_data'
    ]

    fieldsets = (
        ('Transaction Info', {
            'fields': (
                'session_link', 'transaction_type', 'amount',
                'recipient', 'status', 'transaction_ref'
            )
        }),
        ('Response Data', {
            'fields': ('formatted_response_data',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'completed_at')
        }),
    )

    def session_link(self, obj):
        url = reverse('admin:whatsapp_whatsappsession_change', args=[obj.session.pk])
        return format_html('<a href="{}">{}</a>', url, obj.session.phone_number)

    # session_link.short_description = 'Session'

    def formatted_response_data(self, obj):
        return format_html(
            '<pre>{}</pre>',
            json.dumps(obj.response_data, indent=2) if obj.response_data else '{}'
        )

    formatted_response_data.short_description = 'Response Data'


# Add inlines to session admin
WhatsAppSessionAdmin.inlines = [WhatsAppScreenInline, WhatsAppTransactionInline]

