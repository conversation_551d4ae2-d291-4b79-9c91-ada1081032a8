from django.core.management.base import BaseCommand
from django.utils import timezone
from vendboss.services import WhatsAppSessionService
from vendboss.models import WhatsAppSession, WhatsAppScreen


class Command(BaseCommand):
    help = 'Cleanup expired WhatsApp sessions and old screen records'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Delete screen records older than specified days (default: 7)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        days = options['days']

        self.stdout.write(self.style.SUCCESS('Starting WhatsApp session cleanup...'))

        # Cleanup expired sessions
        expired_count = WhatsAppSessionService.cleanup_expired_sessions()
        self.stdout.write(
            self.style.SUCCESS(f'Marked {expired_count} expired sessions')
        )

        # Cleanup old screen records
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        old_screens = WhatsAppScreen.objects.filter(created_at__lt=cutoff_date)
        old_screens_count = old_screens.count()

        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'DRY RUN: Would delete {old_screens_count} screen records older than {days} days'
                )
            )
        else:
            old_screens.delete()
            self.stdout.write(
                self.style.SUCCESS(
                    f'Deleted {old_screens_count} screen records older than {days} days'
                )
            )

        # Cleanup completed sessions older than 30 days
        old_sessions_cutoff = timezone.now() - timezone.timedelta(days=30)
        old_sessions = WhatsAppSession.objects.filter(
            status='COMPLETED',
            updated_at__lt=old_sessions_cutoff
        )
        old_sessions_count = old_sessions.count()

        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'DRY RUN: Would delete {old_sessions_count} completed sessions older than 30 days'
                )
            )
        else:
            old_sessions.delete()
            self.stdout.write(
                self.style.SUCCESS(
                    f'Deleted {old_sessions_count} completed sessions older than 30 days'
                )
            )

        self.stdout.write(self.style.SUCCESS('Cleanup completed successfully!'))

# Usage:
# python manage.py cleanup_whatsapp_sessions
# python manage.py cleanup_whatsapp_sessions --days 14
# python manage.py cleanup_whatsapp_sessions --dry-run
