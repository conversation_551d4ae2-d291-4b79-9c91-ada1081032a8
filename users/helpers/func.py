import string
import secrets
import ipaddress


nigerian_prefixes = {
    # MTN Nigeria
    "234803": "MTN",
    "234806": "MTN",
    "234703": "MTN",
    "234706": "MT<PERSON>",
    "234813": "MT<PERSON>",
    "234816": "MTN",
    "234810": "MTN",
    "234814": "MTN",
    "234903": "MTN",
    "234906": "MTN",
    # Airtel Nigeria
    "234802": "AIRT<PERSON>",
    "234808": "AIRTEL",
    "234708": "AIRTEL",
    "234812": "AIRTEL",
    "234902": "AIRTEL",
    "234907": "AIRTEL",
    # Glo Mobile (Globacom)
    "234805": "GLO",
    "234705": "GL<PERSON>",
    "234905": "GLO",
    # 9mobile
    "234809": "9MOBILE",
    "234817": "9MOBILE",
    "234818": "9MO<PERSON><PERSON>",
    "234908": "9<PERSON><PERSON><PERSON>",
    "234909": "9MOBILE",
    "23491": "9MOBILE",
}


def generate_whatsapp_id():
    from users.models import UserRegistration

    alphabet = string.ascii_uppercase + string.digits
    loop_condition = True
    while loop_condition:
        whatsapp_id = "".join(secrets.choice(alphabet) for i in range(6))
        loop_condition = False

    if UserRegistration.objects.filter(whatsapp_id=whatsapp_id).exists():
        generate_whatsapp_id()

    return whatsapp_id


def vas_transaction_reference():
    from account.models import VASTransaction

    alphabet = string.ascii_lowercase + string.digits
    loop_condition = True
    while loop_condition:
        customer_reference = "".join(secrets.choice(alphabet) for i in range(15))
        if (
            any(c.islower() for c in customer_reference)
            and any(c.islower() for c in customer_reference)
            and sum(c.isdigit() for c in customer_reference) >= 12
        ):
            loop_condition = False
    this_reference = "vendbox-" + customer_reference
    if VASTransaction.objects.filter(customer_reference=this_reference).exists():
        vas_transaction_reference()

    return customer_reference


def get_network_operator(phone_number):
    for prefix, operator in nigerian_prefixes.items():
        if phone_number.startswith(prefix):
            operator_line = operator
            return operator_line
    return "MTN"


def get_client_ip(request):
    """Extract real client IP address."""
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        # may contain multiple IPs (client, proxy1, proxy2…)
        return x_forwarded_for.split(",")[0].strip()
    return request.META.get("REMOTE_ADDR")


def is_public_ip(ip_str):
    """Return True if IP is publicly routable (not private/reserved/etc)."""
    try:
        ip = ipaddress.ip_address(ip_str)
        return ip.is_global  # only True for internet-routable addresses
    except ValueError:
        return False
    

def check_ip_security(ip_str, request_meta=None):
    """
    Check IP for possible risks.
    Returns dict with security flags.
    """
    result = {
        "is_valid": False,
        "is_global": False,
        "is_private": False,
        "is_reserved": False,
        "is_loopback": False,
        "is_link_local": False,
        "is_multicast": False,
        "is_proxy_headers": False,
        "risk": "low"
    }

    try:
        ip = ipaddress.ip_address(ip_str)
        result.update({
            "is_valid": True,
            "is_global": ip.is_global,
            "is_private": ip.is_private,
            "is_reserved": ip.is_reserved,
            "is_loopback": ip.is_loopback,
            "is_link_local": ip.is_link_local,
            "is_multicast": ip.is_multicast,
        })

        # Risk scoring
        if ip.is_private or ip.is_reserved or ip.is_loopback or ip.is_link_local:
            result["risk"] = "high"
        elif not ip.is_global:
            result["risk"] = "medium"

    except ValueError:
        result["risk"] = "invalid"

    # Detect proxy headers if request_meta passed (optional)
    if request_meta:
        suspicious_headers = [
            "HTTP_X_FORWARDED_FOR",
            "HTTP_VIA",
            "HTTP_FORWARDED",
            "HTTP_TRUE_CLIENT_IP",
        ]
        if any(h in request_meta for h in suspicious_headers):
            result["is_proxy_headers"] = True
            result["risk"] = "high"

    return result