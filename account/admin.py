
from django.contrib import admin
from django.db.models.query import QuerySet
from import_export.admin import ImportExportModelAdmin

from account import models, resources
from account.tasks import handle_company_wema_inflow


# Register your model(s) here.

class AccountSystemResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.AccountSystemResource
    search_fields = ["id", "user__phone", "account_number"]
    list_filter = ["account_type", "is_test", "is_active"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class DebitCreditRecordOnAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.DebitCreditRecordOnAccountResource
    search_fields = ["user__phone", "wallet__account__account_number"]
    list_filter = ["date_created", "entry"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class TransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.TransactionResource
    search_fields = [
        "id",
        "user__phone",
        "user_full_name",
        "user_phone_number",
        "transaction_ref",
        "float_to_internal_ref",
        "internal_to_outwards_ref",
        "bank_deposit_ref",
    ]

    list_filter = [
        "status",
        "transfer_stage",
        "transaction_type",
        "date_created",
    ]
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class WalletResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.WalletResource
    search_fields = ["id", "account__id", "account__account_number", "user__phone"]
    list_filter = ["wallet_type", "is_active"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class AccountCreationFailureResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.AccountCreationFailureResource
    search_fields = ["user__phone"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransactionMetaDataResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.TransactionMetaDataResource
    search_fields = ["user__phone"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class CoreBankingCallbackAdmin(ImportExportModelAdmin):
    resource_class = resources.CoreBankingCallbackResource
    search_fields = [
        "recipient_account_number",
        "request_reference",
        "payer_account_number",
        "session_id",
    ]
    list_filter = [
        "one_time",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    @admin.action(description="RESOLVE SALES INFLOWS: resolve pending inflow.")
    def resolve_corebanking_inflow(
        self,
        request,
        queryset: QuerySet[models.CoreBankingCallback],
    ):
        for obj in queryset:
            handle_company_wema_inflow.delay(obj.id)

    actions = [
        resolve_corebanking_inflow,
    ]


class VASTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.VASTransactionResource
    search_fields = ["user__phone"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

admin.site.register(models.AccountSystem, AccountSystemResourceAdmin)
admin.site.register(
    models.DebitCreditRecordOnAccount, DebitCreditRecordOnAccountResourceAdmin
)
admin.site.register(models.Transaction, TransactionResourceAdmin)
admin.site.register(models.Wallet, WalletResourceAdmin)
admin.site.register(models.AccountCreationFailure, AccountCreationFailureResourceAdmin)
admin.site.register(models.TransactionMetaData, TransactionMetaDataResourceAdmin)
admin.site.register(models.CoreBankingCallback, CoreBankingCallbackAdmin)
admin.site.register(models.VASTransaction, VASTransactionResourceAdmin)

