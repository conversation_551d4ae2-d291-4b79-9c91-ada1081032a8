import json
from celery import shared_task
from users.helpers.func import get_network_operator, vas_transaction_reference
from users.tasks import buy_utility_bills_users, vas_service


@shared_task
def handle_company_wema_inflow(inflow_id: str):
    from helpers import enums
    from account import models
    from account.models import VASTransaction, Wallet, Transaction

    inflow_transaction = models.CoreBankingCallback.objects.filter(id=inflow_id).last()
    if inflow_transaction is None:
        return f"INVALID INFLOW TRANSACTION ID: {inflow_id} WAS SUPPLIED."
    # Check for duplicate fund transaction.
    fund_transaction = models.Transaction.objects.filter(
        bank_deposit_ref=inflow_transaction.reference
    ).last()
    if fund_transaction is not None:
        return f"DUPLICATE FUND TRANSACTION FOR INFLOW ID: {inflow_id}."
    # Fetch necessary details.

    one_time = inflow_transaction.one_time
    request_reference = inflow_transaction.request_reference

    recipient_account_number = inflow_transaction.recipient_account_number
    amount_payable = float(inflow_transaction.amount_payable)

    if one_time and request_reference is not None:
        vas_data = VASTransaction.objects.filter(customer_reference=request_reference).first()
        if vas_data is None:
            return "NO ASSOCIATED VAS TO BE UPDATED WITH THE INFLOW."
        
        if vas_data.amount > amount_payable:
            return "INVALID AMOUNT PAYMENET"
        
        if vas_data.vas_initiated:
            return "VAS REFERENCE HAS BEEN PROCESSED BEFORE."
        
        vas_data.vas_initiated = True
        vas_data.save()

        payload = {
            "customerId" : vas_data.customer_id,
            "packageSlug" : vas_data.package_slug,
            "channel" : vas_data.channel,
            "amount" : vas_data.amount,
            "customerName" : vas_data.customer_name,
            "phoneNumber" : vas_data.phone_number,
            "bills_type" : vas_data.bills_type,
            "biller" : vas_data.biller,
            "customer_reference" : vas_data.customer_reference
        }
        vas_data.vas_request = payload
        vas_data.save()
        
        buy_vas_transaction = Transaction.objects.create(
            user=vas_data.user,
            beneficiary_account_number=inflow_transaction.recipient_account_number,
            beneficiary_account_name=inflow_transaction.recipient_account_name,
            bank_code=inflow_transaction.payer_bank_code,
            source_account_name=inflow_transaction.payer_account_name,
            source_account_number=inflow_transaction.payer_account_number,
            user_full_name=vas_data.user.full_name,
            user_email=vas_data.user.email,
            user_phone_number=vas_data.user.phone,
            total_amount_received=inflow_transaction.amount,
            bank_deposit_ref=inflow_transaction.reference,
            transfer_provider=enums.AcctProvider.WEMA,
            date_credited=inflow_transaction.paid_at,
            user_uuid=vas_data.user.id,
            transaction_type=enums.TransactionType.VAS,
            amount=vas_data.amount,
            status=enums.TransactionStatus.PENDING,
            phone_number=vas_data.phone_number,
            vas_transaction_id=vas_data.customer_reference,
            one_time=True,
            narration=f"ONE-TIME VAS PURCHASE FOR {vas_data.phone_number} {inflow_transaction.narration}",
        )

        vas_service(
            payload=payload,
            vas_data_id=vas_data.id,
        )

        return "TRANSACTION PROCESSING FOR VAS"
        
    # Wema account number check.
    account_details = models.AccountSystem.objects.filter(
        account_number=recipient_account_number
    ).last()
    if account_details is None:
        return "NO ASSOCIATED ACCOUNT TO BE UPDATED WITH THE INFLOW."
    associated_user = account_details.user
    meta_data_instance = models.TransactionMetaData.objects.create(
        user=associated_user,
        fund_wallet_payload=json.dumps(inflow_transaction.payload),
    )
    deposit_amount = amount_payable
    if account_details.account_type == enums.AccountType.GENERAL:
        deposit_amount = amount_payable
    elif account_details.account_type == enums.AccountType.AIRTIME:
        deposit_amount = amount_payable
        # commission wallet funding
        
    transaction_instance = models.Transaction.objects.create(
        user=associated_user,
        beneficiary_account_number=inflow_transaction.recipient_account_number,
        beneficiary_account_name=inflow_transaction.recipient_account_name,
        bank_code=inflow_transaction.payer_bank_code,
        source_account_name=inflow_transaction.payer_account_name,
        source_account_number=inflow_transaction.payer_account_number,
        user_full_name=associated_user.full_name,
        user_email=associated_user.email,
        user_phone_number=associated_user.phone,
        transaction_type=enums.TransactionType.DEPOSIT,
        amount=deposit_amount,
        total_amount_received=inflow_transaction.amount,
        bank_deposit_ref=inflow_transaction.reference,
        status=enums.TransactionStatus.PENDING,
        transfer_provider=enums.AcctProvider.WEMA,
        narration=inflow_transaction.narration,
        payout_type=account_details.account_type,
        date_credited=inflow_transaction.paid_at,
    )
    meta_data_instance.transaction = transaction_instance
    meta_data_instance.save()
    # Actual wallet funding
    models.Wallet.fund_wallet(
        account_id=account_details.id,
        transaction_id=transaction_instance.id,
    )
    transaction_instance.refresh_from_db()

    # real-time notification handler.
    title = "MONEY IN"
    body = {
        "wallet_name": f"{account_details.account_name}",
        "payer_name": f"{inflow_transaction.payer_account_name}",
        "date": f"{str(transaction_instance.date_created)}",
        "transaction_type": f"{transaction_instance.transaction_type}",
        "amount": f"{float(transaction_instance.amount)}",
        "balance_before": f"{float(transaction_instance.balance_before)}",
        "balance_after": f"{float(transaction_instance.balance_after)}",
        "payment_channel": "TRANSFER",
        "reference": f"{transaction_instance.bank_deposit_ref}",
        "narration": f"{transaction_instance.narration}",
        "status": f"{transaction_instance.status}",
        
    }
    # update_sales_transaction.delay(
    #     amount=float(inflow_transaction.amount),
    #     inflow_id=inflow_id,
    #     batch_id=request_reference,
    # )
    return "SUCCESSFULLY PROCESSED WEMA BANK INFLOW TRANSACTION."


@shared_task
def send_excess_funds_on_airtime_wallet_to_general_wallet(wallet_id: str):
    from helpers import enums
    from account import models

    wallet_instance = models.Wallet.objects.get(id=wallet_id)
   
    associated_user = wallet_instance.user

    general_wallet = models.Wallet.create_wallet(
        user=associated_user,
        wallet_type=enums.AccountType.GENERAL,
    )
    # meta_data_instance = models.TransactionMetaData.objects.create(
    #     user=associated_user,
    #     fund_wallet_payload=json.dumps(inflow_transaction.payload),
    # )
    wallet_balance = wallet_instance.balance
    if wallet_balance > 2000:
        deposit_amount = wallet_instance.balance - 2000
        airtime_amount = 2000
    else:
        deposit_amount = 0
        airtime_amount = wallet_balance

    if wallet_instance.wallet_type != enums.AccountType.AIRTIME:
        return "WALLET IS NOT AN AIRTIME WALLET, NO ACTION TAKEN."
    if deposit_amount > 0:
    
        buddy_transaction_instance = models.Transaction.objects.create(
            user=associated_user,
            user_uuid=associated_user.id,
            wallet_id=wallet_instance.id,
            wallet_type=enums.AccountType.AIRTIME,
            user_full_name=associated_user.full_name,
            user_email=associated_user.email,
            user_phone_number=associated_user.phone,
            transaction_type=enums.TransactionType.BUDDY,
            amount=deposit_amount,
            total_amount_received=deposit_amount,
            status=enums.TransactionStatus.PENDING,
            # transfer_provider=enums.AcctProvider.WEMA,
            narration=f"BUDDY from Airtime to General Wallet"
        )
        charge_wallet = models.Wallet.charge_wallet(
            wallet_instance=wallet_instance, amount=deposit_amount, transaction_instance=buddy_transaction_instance
        )
        if charge_wallet.get("status", False) is True:

            reference = models.Transaction.create_unique_transaction_ref(suffix="AIR")
            fund_buddy_reference = models.Transaction.create_unique_transaction_ref(
                suffix="AIR"
            )
            wallet_instance = charge_wallet.get("wallet")
            balance_after = charge_wallet.get("balance_after")
            balance_before = charge_wallet.get("balance_before")
            buddy_transaction_instance.balance_before = balance_before
            buddy_transaction_instance.internal_to_internal_ref = reference
            buddy_transaction_instance.balance_after = balance_after
            buddy_transaction_instance.fund_internal_buddy_ref = fund_buddy_reference
            buddy_transaction_instance.transfer_stage = enums.TransferStage.INTERNAL_TO_INTERNAL
            buddy_transaction_instance.beneficiary_wallet_id = general_wallet.id
            buddy_transaction_instance.beneficiary_wallet_type = enums.AccountType.GENERAL
            buddy_transaction_instance.status = enums.TransactionStatus.SUCCESSFUL
            buddy_transaction_instance.save()

            # Actual wallet funding
            models.Wallet.fund_wallet_pay_buddy(
                sender_wallet=wallet_instance,
                receiver_wallet=general_wallet,
                amount=deposit_amount,
                sender_transaction_ins=buddy_transaction_instance,
                payout_type=enums.AccountType.GENERAL,
            )

    network_operator = get_network_operator(phone_number=associated_user.phone)
    if network_operator == "MTN":
        operator = "MTN"
    elif network_operator == "GLO":
        operator = "GLO"
    elif network_operator == "AIRTEL":
        operator = "AIRTEL"
    elif network_operator == "9MOBILE":
        operator = "9MOBILE"
    else:
        operator = "UNKNOWN"

    reference = vas_transaction_reference()
    vas_data = models.VASTransaction.create_transaction(
        user=associated_user,
        customer_reference=reference,
        amount=airtime_amount, 
        provider="CORALPAY", 
        customer_id=associated_user.phone,
        package_slug=f"{operator}_VTU", 
        channel="WEB", 
        customer_name=associated_user.full_name if associated_user.first_name else associated_user.phone,
        phone_number=associated_user.phone, 
        bills_type="VTU", 
        biller=f"{operator}_VTU"
    )

    payload = {
        "customerId": vas_data.customer_id,
        "packageSlug": vas_data.package_slug,
        "channel": vas_data.channel,
        "amount": vas_data.amount,
        "customerName": vas_data.customer_name,
        "phoneNumber": vas_data.phone_number,
        "bills_type": vas_data.bills_type,
        "biller": vas_data.biller,
        "customer_reference": vas_data.customer_reference,
    }
    vas_data.vas_request = payload
    vas_data.save()

    if operator != "UNKNOWN":
        buy_airtime_transaction = models.Transaction.objects.create(
            user=associated_user,
            user_uuid=associated_user.id,
            wallet_id=wallet_instance.id,
            wallet_type=enums.AccountType.AIRTIME,
            user_full_name=associated_user.full_name,
            user_email=associated_user.email,
            user_phone_number=associated_user.phone,
            transaction_type=enums.TransactionType.VAS,
            amount=deposit_amount,
            total_amount_received=deposit_amount,
            status=enums.TransactionStatus.PENDING,
            phone_number=associated_user.phone,
            vas_transaction_id=vas_data.customer_reference,
            network_provider=network_operator,
            narration=f"AIRTIME PURCHASE FOR {associated_user.phone}",
        )
        charge_wallet = models.Wallet.charge_wallet(
            wallet_instance=wallet_instance, amount=airtime_amount, transaction_instance=buy_airtime_transaction
        )
        
        if charge_wallet.get("status", False) is True:
            wallet_instance = charge_wallet.get("wallet")
            balance_after = charge_wallet.get("balance_after")
            balance_before = charge_wallet.get("balance_before")
            buddy_transaction_instance.balance_before = balance_before
            buddy_transaction_instance.balance_after = balance_after
            vas_service(
                payload=payload,
                vas_data_id=vas_data.id,
            )

    return "VTU PURCHASE SUCCESSFUL"