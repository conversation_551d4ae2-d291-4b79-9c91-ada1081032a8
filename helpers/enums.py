from django.db.models import TextChoices


# Create your enumeration type(s) here.
class OTPType(TextChoices):
    REGISTRATION = "REGISTRATION", "REGISTRATION"
    RESET = "RESET", "RESET"
    TRANSACTION = "TRANSACTION", "TRANSACTION"


class AcctProvider(TextChoices):
    WEMA = "WEMA", "WEMA"


class AccountType(TextChoices):
    AIRTIME = "AIRTIME", "Airtime"
    GENERAL = "GENERAL", "General"
    COMMISSIONS = "COMMISSIONS", "Commissions"


class TransactionStatus(TextChoices):
    FAILED = "FAILED", "Failed"
    IGNORE_HISTORY = "IGNORE_HISTORY", "Ignore History"
    IN_PROGRESS = "IN_PROGRESS", "In Progress"
    PENDING = "PENDING", "Pending"
    REVERSED = "REVERSED", "Reversed"
    SUCCESSFUL = "SUCCESSFUL", "Successful"
    SCHEDULED = "SCHEDULED", "Scheduled"  # Waiting for a future run
    RUNNING = "RUNNING", "Running"  # Currently executing (for scheduled)
    CANCELLED = "CANCELLED", "Cancelled"  # Transfer was canceled
    EXPIRED = "EXPIRED", "Expired"  # Scheduled transfer missed execution
    COMPLETED = "COMPLETED", "Completed"  # All transfers successful
    PARTIALLY_COMPLETED = "PARTIALLY_COMPLETED", "Partially Completed"  # Some items succeeded


class TransferStage(TextChoices):
    DEDUCTED = "DEDUCTED", "Deducted"
    DEBIT = "DEBIT", "Debit"
    FLOAT_TO_INTERNAL = "FLOAT_TO_INTERNAL", "Float To Internal"
    FLOAT_TO_EXTERNAL = "FLOAT_TO_EXTERNAL", "Float To External"
    INTERNAL_TO_FLOAT = "INTERNAL_TO_FLOAT", "Internal To Float"
    INTERNAL_TO_INTERNAL = "INTERNAL_TO_INTERNAL", "Internal To Internal"
    INTERNAL_TO_OUTWARDS = "INTERNAL_TO_OUTWARDS", "Internal To Outwards"


class DebitCreditEntry(TextChoices):
    CREDIT = "CREDIT", "Credit"
    DEBIT = "DEBIT", "Debit"
    REVERSAL = "REVERSAL", "Reversal"


class TransactionType(TextChoices):
    ADD_FUND_LIBERTYPAY = "ADD_FUND_LIBERTYPAY", "Add Fund Libertypay"
    AIRTIME_TOP_UP = "AIRTIME_TOP_UP", "Airtime Top Up"
    WALLET_TRANSFER = "WALLET_TRANSFER", "Wallet Transfer"
    BUDDY = "BUDDY", "Buddy"
    FUND_BUDDY = "FUND_BUDDY", "Fund Buddy"
    CARD = "CARD", "Card"
    COMMISSION = "COMMISSION", "Commission"
    DEPOSIT = "DEPOSIT", "Deposit"
    DUPLICATE_FUND_WALLET = "DUPLICATE_FUND_WALLET", "Duplicate Fund Wallet"
    FUND_WALLET = "FUND_WALLET", "Fund Wallet"
    RETRIEVAL = "RETRIEVAL", "Retrieval"
    REVERSAL = "REVERSAL", "Reversal"
    VAS = "VAS", "Value Added Service"


class BillsType(TextChoices):
    DATA_BUNDLE = "DATA_BUNDLE", "DATA_BUNDLE"
    ELECTRICITY = "ELECTRICITY", "ELECTRICITY",
    SUBSCRIPTION = "SUBSCRIPTION", "SUBSCRIPTION",
    BETTING = "BETTING", "BETTING",
    CABLE_TV = "CABLE_TV", "CABLE_TV",
    VTU = "VTU", "VTU",
    AIRTIME_PRINT = "AIRTIME_PRINT", "AIRTIME_PRINT",
    OTHER = "OTHER", "OTHER",


class BillProvider(TextChoices):
    CORALPAY = "CORALPAY", "CORALPAY"
    REDBILLER = "REDBILLER", "REDBILLER"