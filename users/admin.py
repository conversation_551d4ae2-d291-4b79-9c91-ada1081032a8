from django.contrib import admin

# Register your models here.
from django.contrib import admin
from django.contrib.admin.models import DELETION, LogEntry
from django.db.models.query import QuerySet
from django.urls import reverse
from django.utils.html import escape
from django.utils.safestring import mark_safe
from import_export.admin import ImportExportModelAdmin

from users import models, resources


@admin.register(LogEntry)
class LogEntryAdmin(ImportExportModelAdmin):
    date_hierarchy = "action_time"

    list_filter = [
        "user",
        "content_type",
        "action_flag",
    ]

    search_fields = [
        "object_repr",
        "change_message",
    ]

    list_display = [
        "action_time",
        "user",
        "content_type",
        "object_link",
        "action_flag",
    ]

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser

    def object_link(self, obj):
        if obj.action_flag == DELETION:
            link = escape(obj.object_repr)
        else:
            ct = obj.content_type
            link = '<a href="%s">%s</a>' % (
                reverse(
                    "admin:%s_%s_change" % (ct.app_label, ct.model),
                    args=[obj.object_id],
                ),
                escape(obj.object_repr),
            )
        return mark_safe(link)

    object_link.admin_order_field = "object_repr"
    object_link.short_description = "object"


class ProfileResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.UserResource
    search_fields = ["phone"]
    list_filter = ["is_superuser", "is_staff"]
    list_display = (
        "id",
        "is_superuser",
        "is_staff",
        "is_active",
        "is_admin",
        "email",
        "phone",
        "first_name",
        "last_name",
        "bvn",
        "email_verified",
        "created_at",
        "updated_at",
    )
class AssociatedUserResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.AssociatedUserResource
    search_fields = ["user__phone"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class UserRegistrationResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.UserRegistrationResource
    search_fields = ["user__phone"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class ConstantTableResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ConstantTableResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

admin.site.register(models.Profile, ProfileResourceAdmin)
admin.site.register(models.AssociatedUser, AssociatedUserResourceAdmin)
admin.site.register(models.UserRegistration, UserRegistrationResourceAdmin)
admin.site.register(models.ConstantTable, ConstantTableResourceAdmin)