from helpers import enums
from helpers.enums import (
    <PERSON><PERSON><PERSON><PERSON>, <PERSON>Type, AccountType, <PERSON><PERSON>t<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 
    <PERSON><PERSON><PERSON>, TransactionStatus
)
from helpers.core_banking_api import WemaApi
from users.helpers.func import vas_transaction_reference
from django.db.models import Max
from celery import shared_task
from typing import Optional

from vendboss.services import WhatsAppAPIService
from django.conf import settings
from django.utils import timezone

PHONE_NUMBER_ID = settings.PHONE_NUMBER_ID

@shared_task
def vas_service(
    payload: dict,
    vas_data_id: str,
    ):
    from account.models import VASTransaction, Transaction
    from helpers.libertypay import LibertyPay
    from account.models import VASTransaction
    from vendboss.views import WhatsAppWebhookAPIView

    vas_data = VASTransaction.objects.get(id=vas_data_id)

    response = LibertyPay.vend_vas_service(
        payload=payload
    )
    transaction_id = response.get("data", {}).get("transaction_id", None)
    vas_data.vas_response = response
    vas_data.libertypay_reference = transaction_id
    vas_data.save()

    if transaction_id:
        transaction_ins = Transaction.objects.filter(vas_transaction_id=vas_data.customer_reference).first()
        if transaction_ins:
            transaction_ins.vas_libertypay_reference = transaction_id
            transaction_ins.save()
    
        message = f"Processing Transaction... 🔄⏳!\n\nReference: {vas_data.customer_reference}\n"
        message += f"Amount: {vas_data.amount}\nPackage: {vas_data.package_slug}\n"
        message += f"phone Number: {vas_data.phone_number}"
        WhatsAppAPIService.send_message(phone_number=vas_data.phone_number, message=message, phone_number_id=PHONE_NUMBER_ID)
        WhatsAppAPIService.send_message(phone_number="*************", message=message, phone_number_id=PHONE_NUMBER_ID)

@shared_task
def buy_utility_bills(
        user_id: str, amount: float, provider: BillProvider, 
        customer_id: str, package_slug: str, customer_name: str, 
        phone_number: str, bills_type: BillsType, biller: str
    ):
    from account.models import Wallet, Transaction, VASTransaction
    from users.models import Profile

    user = Profile.objects.get(id=user_id)
    
    user_wallet = Wallet.create_wallet(
        user=user, 
        wallet_type=enums.AccountType.GENERAL
    )

    reference = vas_transaction_reference()
    vas_data = VASTransaction.create_transaction(
        user=user,
        customer_reference=reference,
        amount=amount, 
        provider=provider, 
        customer_id=customer_id,
        package_slug=package_slug, 
        channel="WEB", 
        customer_name=customer_name,
        phone_number=phone_number, 
        bills_type=bills_type, 
        biller=biller
    )

    payload = {
        "customerId" : vas_data.customer_id,
        "packageSlug" : vas_data.package_slug,
        "channel" : vas_data.channel,
        "amount" : vas_data.amount,
        "customerName" : vas_data.customer_name,
        "phoneNumber" : vas_data.phone_number,
        "bills_type" : vas_data.bills_type,
        "biller" : vas_data.biller,
        "customer_reference" : vas_data.customer_reference
    }
    vas_data.vas_request = payload
    vas_data.save()

    buy_vas_transaction = Transaction.objects.create(
        user=user,
        user_uuid=user.id,
        wallet_id=user_wallet.id,
        wallet_type=enums.AccountType.GENERAL,
        user_full_name=user.full_name,
        user_email=user.email,
        user_phone_number=user.phone,
        transaction_type=enums.TransactionType.VAS,
        amount=amount,
        status=enums.TransactionStatus.PENDING,
        phone_number=phone_number,
        vas_transaction_id=vas_data.customer_reference,
        # network_provider="MTN",
        narration=f"VAS PURCHASE FOR {phone_number}",
    )
    charge_wallet = Wallet.charge_wallet(
        wallet_instance=user_wallet, amount=amount, transaction_instance=buy_vas_transaction
    )
    if charge_wallet.get("status", False) is True:
    
        vas_service(
            payload=payload,
            vas_data_id=vas_data.id,
        )
    return reference

@shared_task
def buy_utility_bills_users(
        reference: str,
    ):
    from account.models import Wallet, Transaction, VASTransaction

    vas_data = VASTransaction.objects.get(customer_reference=reference)
    if vas_data.vas_initiated:
        return reference
    
    vas_data.vas_initiated = True
    vas_data.save()

    user_wallet = Wallet.create_wallet(
        user=vas_data.user, 
        wallet_type=enums.AccountType.GENERAL
    )

    payload = {
        "customerId" : vas_data.customer_id,
        "packageSlug" : vas_data.package_slug,
        "channel" : vas_data.channel,
        "amount" : vas_data.amount,
        "customerName" : vas_data.customer_name,
        "phoneNumber" : vas_data.phone_number,
        "bills_type" : vas_data.bills_type,
        "biller" : vas_data.biller,
        "customer_reference" : vas_data.customer_reference
    }
    vas_data.vas_request = payload
    vas_data.save()

    buy_vas_transaction = Transaction.objects.create(
        user=vas_data.user,
        user_uuid=vas_data.user.id,
        wallet_id=user_wallet.id,
        wallet_type=enums.AccountType.GENERAL,
        user_full_name=vas_data.user.full_name,
        user_email=vas_data.user.email,
        user_phone_number=vas_data.user.phone,
        transaction_type=enums.TransactionType.VAS,
        amount=vas_data.amount,
        status=enums.TransactionStatus.PENDING,
        phone_number=vas_data.phone_number,
        vas_transaction_id=vas_data.customer_reference,
        # network_provider="MTN",
        narration=f"VAS PURCHASE FOR {vas_data.phone_number}",
    )
    charge_wallet = Wallet.charge_wallet(
        wallet_instance=user_wallet, amount=vas_data.amount, transaction_instance=buy_vas_transaction
    )
    if charge_wallet.get("status", False) is True:
    
        vas_service(
            payload=payload,
            vas_data_id=vas_data.id,
        )
    return reference

@shared_task
def create_user_account_wallet(
    user_id: str,
    store_name: Optional[str] = None,
):
    from account.models import AccountSystem, AccountCreationFailure, Wallet
    from users.models import Profile
    # Check if the user already has an account with the same phone number
    
    user = Profile.objects.get(id=user_id)
    todays_date = timezone.now().date()
    first_name = f"/{todays_date}"

    airtime_wallet = Wallet.create_wallet(user=user, wallet_type=AccountType.AIRTIME)
    general_wallet = Wallet.create_wallet(user=user, wallet_type=AccountType.GENERAL)
    create_wallet = Wallet.create_wallet(user=user, wallet_type=AccountType.COMMISSIONS)

    all_user_account = AccountSystem.objects.filter(user__phone=user.phone)
    if all_user_account.exists():
        count = all_user_account.aggregate(Max("phone_number_count"))["phone_number_count__max"]
    else:
        count = -1  # Start from -1 if no accounts exist


    get_airtime_account_number = AccountSystem.objects.filter(user__phone=user.phone, account_type="AIRTIME").last()
    if get_airtime_account_number is None:
        if count == -1:
            count +=1
            response = WemaApi.create_account_number(phone_number=user.phone, first_name=first_name)
        else:
            count += 1
            formatted_phone_number = f"{user.phone}+{count}"
            response = WemaApi.create_account_number(phone_number=formatted_phone_number, first_name=first_name)
        if response.get("status_code") == 201:
            response_data = response.get("data",{}).get("data",{}).get("account_details")
            _account = AccountSystem.objects.create(
                user=user,
                account_provider=AcctProvider.WEMA,
                account_number=response_data.get("account_number"),
                account_name=response_data.get("first_name"),
                account_type="AIRTIME",
                bank_name="Wema Bank Plc",
                bank_code="000017",
                request_status=TransactionStatus.SUCCESSFUL,
                payload=response_data,
                phone_number_count=count,  # Increment the count for the phone number
            )
            airtime_wallet.account = _account
            airtime_wallet.save()
        else:
            AccountCreationFailure.objects.create(
                user=user,
                payload=response,
                account_type="AIRTIME",
                account_provider=AcctProvider.WEMA,
            )
    
    get_airtime_account_number = AccountSystem.objects.filter(user__phone=user.phone, account_type="GENERAL").last()
    if get_airtime_account_number is None:
        if count == -1:
            count +=1
            response = WemaApi.create_account_number(phone_number=user.phone, first_name=first_name)
        else:
            count += 1
            formatted_phone_number = f"{user.phone}+{count}"
            response = WemaApi.create_account_number(phone_number=formatted_phone_number, first_name=first_name)

        if response.get("status_code") == 201:
            response_data = response.get("data",{}).get("data",{}).get("account_details")
            _account = AccountSystem.objects.create(
                user=user,
                account_provider=AcctProvider.WEMA,
                account_number=response_data.get("account_number"),
                account_name=response_data.get("first_name"),
                account_type="GENERAL",
                bank_name="Wema Bank Plc",
                bank_code="000017",
                request_status=TransactionStatus.SUCCESSFUL,
                payload=response_data,
                phone_number_count=count,
            )
            general_wallet.account = _account
            general_wallet.save()
        else:
            AccountCreationFailure.objects.create(
                user=user,
                payload=response,
                account_type="GENERAL",
                account_provider=AcctProvider.WEMA,
            )

@shared_task
def verify_vas_transaction():
    from account.models import VASTransaction, Transaction
    from helpers.libertypay import LibertyPay
    from account.models import VASTransaction
    from vendboss.views import WhatsAppWebhookAPIView
    from vendboss.services import WhatsAppSessionService

    all_pending_vas_transaction = Transaction.objects.filter(status="PENDING", transaction_type="VAS", vas_libertypay_reference__isnull=False)
    for pending_vas_transaction in all_pending_vas_transaction:

        liberty_transaction_ref = pending_vas_transaction.vas_libertypay_reference
        response = LibertyPay.bills_payment_status(transaction_id=liberty_transaction_ref)

        transaction_status = response.get("data", {}).get("transaction_status", "PENDING")
        if transaction_status == "SUCCESSFUL":
            # pending_vas_transaction.status = "FAILED"
            pending_vas_transaction.status = "SUCCESSFUL"
            pending_vas_transaction.save()
            vas_data = VASTransaction.objects.filter(libertypay_reference=liberty_transaction_ref).first()
            if vas_data:
                vas_data.status = "SUCCESSFUL"
                vas_data.save()

                if vas_data.whatsapp_session:
                    message = f"Transaction Successful ✅💰!\n\nReference: {vas_data.customer_reference}\n"
                    message += f"Amount: {vas_data.amount}\nPackage: {vas_data.package_slug}\n"
                    message += f"phone Number: {vas_data.phone_number}"
                    WhatsAppAPIService.send_message(phone_number=vas_data.phone_number, message=message, phone_number_id=PHONE_NUMBER_ID)
                    

    return "VERIFCATION OF PENDING VAS SUCCESSFUL"
         