from rest_framework import permissions, status
from rest_framework.exceptions import PermissionDenied, APIException

from account.models import Wallet
from users.helpers.func import check_ip_security, get_client_ip, is_public_ip
from users.models import ConstantTable


class DoNotAllowException(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "Store not activated for Purchase. "
        "Please contact support for further assistance.",
    }
    default_code = "Not permitted"


class SuspiciousIPException(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "You have been blacklisted due to suspicious activities. "
        "Please contact support for further assistance.",
    }
    default_code = "Not permitted"


class IsMerchantActivated(permissions.BasePermission):

    def has_permission(self, request, view):
        user_instance = request.user

        constant_table = ConstantTable.get_constant_instance()
        activation_amount = constant_table.activation_amount

        wallet_balance = Wallet.wallet_balance(user=user_instance, wallet_type="GENERAL")
        if wallet_balance < activation_amount:
            raise DoNotAllowException
        else:
            return True
        

class BlockVPNProxyMiddleware(permissions.BasePermission):
    def has_permission(self, request, view):

        client_ip = get_client_ip(request)

        if client_ip and not is_public_ip(client_ip):
            raise SuspiciousIPException

        suspicious_headers = [
            # "HTTP_X_FORWARDED_FOR",
            "HTTP_X_FORWARDED_HOST",
            "HTTP_X_FORWARDED_PROTO",
            "HTTP_VIA",
            "HTTP_FORWARDED",
            "HTTP_TRUE_CLIENT_IP",
        ]
        # Check if any proxy-related headers exist
        for header in suspicious_headers:
            if header in request.META:
                raise SuspiciousIPException
            
        ip_info = check_ip_security(client_ip, request.META)

        if ip_info["risk"] == "high":
            raise SuspiciousIPException
        return True

        