from django.db import models

from account.tasks import handle_company_wema_inflow, send_excess_funds_on_airtime_wallet_to_general_wallet
from helpers import enums
from helpers.core_banking_api import WemaApi
from helpers.enums import (
    AccountType, AcctProvider, BillProvider, BillsType, DebitCreditEntry, TransactionStatus, TransactionType, TransferStage
)
from main.models import BaseModel
from users.helpers.func import vas_transaction_reference
from users.models import Profile, UserRegistration
from django.core.validators import MinValueValidator
from django.db import IntegrityError, models, transaction
from django.db.models import F, <PERSON>
import json
import time
import uuid
from django.utils import timezone
from django.conf import settings
from users.tasks import buy_utility_bills
from vendboss.models import WhatsAppSession
from typing import Optional
# Create your models here.


class AccountSystem(BaseModel):
    user = models.ForeignKey(Profile, related_name="accounts", on_delete=models.CASCADE)
    account_provider = models.Char<PERSON>ield(
        max_length=300, choices=AcctProvider.choices, default=AcctProvider.WEMA
    )
    account_number = models.CharField(
        max_length=250, unique=True, null=False, blank=False
    )
    account_name = models.CharField(max_length=250, null=True, blank=True)
    account_type = models.CharField(
        max_length=250,
        choices=AccountType.choices,
        default=AccountType.AIRTIME,
    )
    bank_name = models.CharField(max_length=250, null=True, blank=True)
    bank_code = models.CharField(max_length=250, null=True, blank=True)
    is_test = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    request_status = models.CharField(
        max_length=250,
        choices=TransactionStatus.choices,
        null=True,
        blank=True,
    )
    phone_number_count = models.IntegerField(default=0)
    updated = models.BooleanField(default=False)
    available_balance = models.FloatField(
        default=0.0, validators=[MinValueValidator(0.0)]
    )
    payload = models.TextField(null=True)

    def __str__(self) -> str:
        return f"{self.account_type.lower()}-{self.id}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ACCOUNT SYSTEM"
        verbose_name_plural = "ACCOUNT SYSTEM"
        ordering = ["-created_at"]


class AccountCreationFailure(BaseModel):
    user = models.ForeignKey(
        Profile,
        on_delete=models.CASCADE,
        related_name="creation_fails",
    )
    account_type = models.CharField(
        max_length=250,
        choices=AccountType.choices,
        null=True,
        blank=True,
    )
    account_provider = models.CharField(max_length=300, null=True, blank=True)
    is_test = models.BooleanField(default=False)
    payload = models.TextField(null=True, blank=True)
    request_payload = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ACCOUNT CREATION FAILURE"
        verbose_name_plural = "ACCOUNT CREATION FAILURE"
        ordering = ["-created_at"]


class Wallet(BaseModel):
    user = models.ForeignKey(Profile, on_delete=models.CASCADE, null=True, blank=True)
    account = models.ForeignKey(
        AccountSystem, on_delete=models.CASCADE, null=True, blank=True
    )
    balance = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    previous_balance = models.FloatField(
        default=0.0, validators=[MinValueValidator(0.0)]
    )
    wallet_type = models.CharField(
        max_length=250,
        choices=AccountType.choices,
        default=AccountType.AIRTIME,
    )
    is_active = models.BooleanField(default=True)

    def __str__(self) -> str:
        return f"wallet-{self.id}"

    def save(self, *args, **kwargs):

        try:
            if self.pk:
                self.previous_balance = self.__original_amount

            return super(Wallet, self).save(*args, **kwargs)
        except Exception as err:
            raise Exception(f"{err}")

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "WALLET SYSTEM"
        verbose_name_plural = "WALLET SYSTEM"
        ordering = ["-created_at"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__original_amount = self.balance

    @classmethod
    def create_wallet(cls, user: Profile, wallet_type: AccountType):
        """
        Create a wallet for the user with the specified wallet type.
        If the user already has a wallet of that type, it will return the existing one.
        """


        existing_wallet = cls.objects.filter(user=user, wallet_type=wallet_type).first()
        if existing_wallet:
            return existing_wallet
        else:
            new_wallet = cls.objects.create(
                user=user,
                wallet_type=wallet_type,
            )
            return new_wallet

    @classmethod
    def transfer_to_wallet(
        cls,
        sender_number_account_number: str,
        recipient_account_number: str,
        amount: float,
        narration: str = "",
        lookup_method="ACCOUNT_NUMBER",
    ):

        if amount <= 0:
            raise ValueError("Transfer amount must be greater than zero.")

        if lookup_method == "ACCOUNT_NUMBER":

            sender_wallet = Wallet.objects.filter(
                account__account_number=sender_number_account_number, is_active=True
            ).first()
            recipient_wallet = Wallet.objects.filter(
                account__account_number=recipient_account_number, is_active=True
            ).first()

            if not sender_wallet:
                return {
                    "status": False,
                    "message": f"Sender's wallet not found or inactive for account number: {sender_number_account_number}.",
                }
            if not recipient_wallet:
                return {
                    "status": False,
                    "message": f"Recipient's wallet not found or inactive for account number: {recipient_account_number}.",
                }
            if sender_wallet.balance < amount:
                return {
                    "status": False,
                    "message": f"Insufficient balance in sender's wallet. Current balance: {sender_wallet.balance}, attempted transfer amount: {amount}.",
                }

            sender_acct_instance = sender_wallet.account
            sender = sender_acct_instance.user
            #########################################################
            recipient_wallet_acct_instance = recipient_wallet.account
            recipient = recipient_wallet_acct_instance.user

        # Perform atomic transaction
        with transaction.atomic():
            sender_balance_before = sender_wallet.balance
            sender_wallet.balance = F("balance") - amount
            sender_wallet.save()
            sender_wallet.refresh_from_db()

            sender_balance_after = sender_wallet.balance

            sender_debit_txn_instance = Transaction.objects.create(
                transaction_type=TransactionType.AIRTIME_TOP_UP,
                user=sender,
                # company_name=company_name,
                user_full_name=sender.full_name,
                user_uuid=sender.id,
                user_email=sender.email,
                payout_type=sender_acct_instance.account_type,
                balance_before=sender_balance_before,
                balance_after=sender_balance_after,
                # float_to_external_ref=float_to_external_ref,
                # float_to_internal_ref=float_to_internal_ref,
                amount=amount,
                # commission=commission,
                # stamp_duty=vfd_stamp_duty,
                # provider_fee=provider_fee,
                debit_amount=amount,
                beneficiary_account_number=recipient_account_number,
                beneficiary_account_name=recipient_wallet_acct_instance.account_name,
                # bank_name="PAYBOX",
                status=TransactionStatus.SUCCESSFUL,
                narration=f"Debit: Wallet transfer to {recipient_account_number} from {sender_number_account_number}",
                # bank_code=bank_code,
                source_account_name=sender_acct_instance.account_name,
                source_account_number=sender_acct_instance.account_number,
                # company_id=company_id,
                # transfer_provider=AcctProvider.VFD,
                # pension_data_id=pension_data_id,
            )

            # Create sender's debit record
            DebitCreditRecordOnAccount.objects.create(
                user=sender,
                wallet=sender_wallet,
                entry=DebitCreditEntry.DEBIT,
                wallet_type=sender_wallet.wallet_type,
                balance_before=sender_balance_before,
                balance_after=sender_balance_after,
                amount=amount,
                type_of_trans="Pay Buddy",
                transaction_instance_id=str(sender_debit_txn_instance.transaction_ref),
                # date_credited=now(),
            )

            #####################################################################################################################

            recipient_balance_before = recipient_wallet.balance

            recipient_wallet.balance = F("balance") + amount
            recipient_wallet.save()
            recipient_wallet.refresh_from_db()

            recipient_balance_after = recipient_wallet.balance

            recipient_credit_txn_instance = Transaction.objects.create(
                transaction_type=TransactionType.FUND_BUDDY,
                user=recipient,
                # company_name=company_name,
                user_full_name=recipient.full_name,
                user_uuid=recipient.id,
                user_email=recipient.email,
                payout_type=recipient_wallet_acct_instance.account_type,
                balance_before=recipient_balance_before,
                balance_after=recipient_balance_after,
                # float_to_external_ref=float_to_external_ref,
                # float_to_internal_ref=float_to_internal_ref,
                amount=amount,
                # commission=commission,
                # stamp_duty=vfd_stamp_duty,
                # provider_fee=provider_fee,
                debit_amount=amount,
                beneficiary_account_number=recipient_account_number,
                beneficiary_account_name=recipient_wallet_acct_instance.account_name,
                # bank_name="User wallet",
                status=TransactionStatus.SUCCESSFUL,
                narration=f"Credit: Wallet transfer from {sender_acct_instance.account_name} to your wallet {recipient_account_number}",
                # bank_code=bank_code,
                source_account_name=sender_acct_instance.account_name,
                source_account_number=sender_acct_instance.account_number,
                # company_id=company_id,
                # transfer_provider=AcctProvider.VFD,
                # pension_data_id=pension_data_id,
            )

            # Create sender's debit record
            DebitCreditRecordOnAccount.objects.create(
                user=recipient,
                wallet=recipient_wallet,
                entry=DebitCreditEntry.CREDIT,
                wallet_type=recipient_wallet.wallet_type,
                balance_before=recipient_balance_before,
                balance_after=recipient_balance_after,
                amount=amount,
                type_of_trans="Pay Buddy",
                transaction_instance_id=str(
                    recipient_credit_txn_instance.transaction_ref
                ),
                # date_credited=now(),
            )

            return {
                "status": True,
                "message": "Transaction completed successfully.",
                "amount_funded": amount,
                "recipient_account_number": recipient_account_number,
                "recipient_account_name": recipient_wallet_acct_instance.account_name,
            }
        
    @classmethod
    def create_wallet_object(cls, account_ins, user_id, wallet_type):
        wallet_ins = cls.objects.create(
            user_id=user_id, account=account_ins, wallet_type=wallet_type
        )
        return wallet_ins
    
    @classmethod
    def create_wema_virtual_accounts(
        user_id: str,
    ):
        from account.models import AccountSystem, AccountCreationFailure, Wallet
        from users.models import Profile
        # Check if the user already has an account with the same phone number
        
        user = Profile.objects.get(id=user_id)

        airtime_wallet = Wallet.create_wallet(user=user, wallet_type=AccountType.AIRTIME)
        general_wallet = Wallet.create_wallet(user=user, wallet_type=AccountType.GENERAL)
        create_wallet = Wallet.create_wallet(user=user, wallet_type=AccountType.COMMISSIONS)

        all_user_account = AccountSystem.objects.filter(user__phone=user.phone)
        if all_user_account.exists():
            count = all_user_account.aggregate(Max("phone_number_count"))["phone_number_count__max"]
        else:
            count = -1  # Start from -1 if no accounts exist

        get_airtime_account_number = AccountSystem.objects.filter(user__phone=user.phone, account_type="AIRTIME").last()
        if get_airtime_account_number is None:
            if count == -1:
                response = WemaApi.create_account_number(phone_number=user.phone, first_name=user.first_name)
            else:
                count += 1
                formatted_phone_number = f"{user.phone}+{count}"
                response = WemaApi.create_account_number(phone_number=formatted_phone_number, first_name=user.first_name)
            if response.get("status_code") == 201:
                response_data = response.get("data",{}).get("data",{}).get("account_details")
                _account = AccountSystem.objects.create(
                    user=user,
                    account_provider=AcctProvider.WEMA,
                    account_number=response_data.get("account_number"),
                    account_name=response_data.get("company_name"),
                    account_type="AIRTIME",
                    bank_name="Wema Bank Plc",
                    bank_code="000017",
                    request_status=TransactionStatus.SUCCESSFUL,
                    payload=response_data,
                    phone_number_count=count,  # Increment the count for the phone number
                )
                airtime_wallet.account = _account
                airtime_wallet.save()
            else:
                AccountCreationFailure.objects.create(
                    user=user,
                    payload=response,
                    account_type="AIRTIME",
                    account_provider=AcctProvider.WEMA,
                )
            
        get_airtime_account_number = AccountSystem.objects.filter(user__phone=user.phone, account_type="GENERAL").last()
        if get_airtime_account_number is None:
            if count == -1:
                response = WemaApi.create_account_number(phone_number=user.phone, first_name=user.first_name)
            else:
                count += 1
                formatted_phone_number = f"{user.phone}+{count}"
                response = WemaApi.create_account_number(phone_number=formatted_phone_number, first_name=user.first_name)

            if response.get("status_code") == 201:
                response_data = response.get("data",{}).get("data",{}).get("account_details")
                _account = AccountSystem.objects.create(
                    user=user,
                    account_provider=AcctProvider.WEMA,
                    account_number=response_data.get("account_number"),
                    account_name=response_data.get("company_name"),
                    account_type="GENERAL",
                    bank_name="Wema Bank Plc",
                    bank_code="000017",
                    request_status=TransactionStatus.SUCCESSFUL,
                    payload=response_data,
                    phone_number_count=count,
                )
                general_wallet.account = _account
                general_wallet.save()
            else:
                AccountCreationFailure.objects.create(
                    user=user,
                    payload=response,
                    account_type="GENERAL",
                    account_provider=AcctProvider.WEMA,
                )
            
    @classmethod
    def fund_wallet(cls, account_id, transaction_id, webhook_notification=False):

        with transaction.atomic():
            initial_transaction_record = Transaction.objects.get(id=transaction_id)
            account = AccountSystem.objects.get(id=account_id)

            if webhook_notification:
                Transaction.objects.filter(txn_id=str(transaction_id)).update(
                    status=TransactionStatus.SUCCESSFUL
                )

            amount = initial_transaction_record.amount
            debit_credit_record = DebitCreditRecordOnAccount.objects.create(
                user=account.user,
                entry=DebitCreditEntry.CREDIT,
                wallet_type=account.account_type,
                amount=amount,
                transaction_instance_id=initial_transaction_record.id,
                date_credited=timezone.now(),
            )

            # fund wallet
            wallet_instance = cls.objects.get(account=account)
            previous_balance = wallet_instance.balance
            wallet_instance.balance = F("balance") + amount
            wallet_instance.save()

            wallet_instance.refresh_from_db()
            updated_balance = wallet_instance.balance

            debit_credit_record.wallet = wallet_instance
            debit_credit_record.balance_before = previous_balance
            debit_credit_record.balance_after = updated_balance
            debit_credit_record.save()

            initial_transaction_record.balance_before = previous_balance
            initial_transaction_record.balance_after = updated_balance
            initial_transaction_record.status = TransactionStatus.SUCCESSFUL
            initial_transaction_record.date_credited = timezone.now()
            initial_transaction_record.save()

            # if webhook_notification:
            #     cls.bank_deposit_email_notification(
            #         recipient=account.user.email,
            #         amount=amount,
            #         balance_after=updated_balance,
            #         acct_name=account.account_name,
            #         account_number=account.account_number,
            #         wallet_type=account.account_type,
            #         source_nuban=initial_transaction_record.source_account_number,
            #         source_acct_name=initial_transaction_record.source_account_name,
            #         narration=initial_transaction_record.narration,
            #     )

            if wallet_instance.wallet_type == AccountType.AIRTIME:
                send_excess_funds_on_airtime_wallet_to_general_wallet(
                    wallet_id=wallet_instance.id,
                )

            return initial_transaction_record.status
        
    @classmethod
    def fund_wallet_pay_buddy(
        cls,
        sender_wallet: "Wallet",
        receiver_wallet: "Wallet",
        amount,
        sender_transaction_ins: "Transaction",
        payout_type,
    ):

        wallet = receiver_wallet

        # create Transaction for receiver
        transaction = Transaction.objects.create(
            user=wallet.user,
            wallet_id=wallet.id,
            user_uuid=wallet.user.id,
            wallet_type=payout_type,
            transaction_type=enums.TransactionType.BUDDY,
            user_full_name=wallet.user.full_name,
            user_email=wallet.user.email,
            payout_type=payout_type,
            amount=amount,
            source_wallet_id=sender_wallet.id,
            source_wallet_type=sender_wallet.wallet_type,
            transfer_stage=enums.TransferStage.INTERNAL_TO_INTERNAL,
            narration=sender_transaction_ins.narration,
            internal_to_internal_ref=sender_transaction_ins.fund_internal_buddy_ref,
            status=enums.TransactionStatus.PENDING,
        )
        # get receiver balance after and credit
        balance_before = wallet.balance
        balance_after = wallet.balance + amount
        wallet.balance = balance_after
        wallet.save()

        # update wallet balance after for transaction
        transaction.balance_before = balance_before
        transaction.balance_after = balance_after
        transaction.status = enums.TransactionStatus.SUCCESSFUL
        transaction.save()

        # credit receiver
        credit_record = DebitCreditRecordOnAccount.objects.create(
            user=wallet.user,
            entry="CREDIT",
            wallet_type=payout_type,
            wallet=wallet,
            balance_before=balance_before,
            amount=amount,
            balance_after=balance_after,
            transaction_instance_id=transaction.id,
        )

        return {
            "balance_before": balance_before,
            "balance_after": balance_after,
            "record": credit_record,
            "wallet_instance": wallet,
            "transaction_ref": transaction.transaction_ref,
        }
        
    @classmethod
    def has_sufficient_funds(
        cls, user, wallet_type: AccountType, amount: float, account: AccountSystem
    ) -> dict:
        """
        Determines if a user has sufficient funds in a specified wallet to cover a given amount.

        This method checks if the user has a wallet of the specified type with a balance
        that is greater than or equal to the amount specified.

        Args:
            user (User): The user whose wallet balance is being checked.
            wallet_type (AccountType): The type of wallet to check (e.g., savings, current).
            amount (float): The amount to verify against the wallet's balance.

        Returns:
            dict: A dictionary containing:
                - "is_chargeable" (bool): True if the wallet has sufficient funds, otherwise False.
                - "wallet_instance" (Wallet or None): The wallet instance that meets the criteria,
                or None if no such wallet exists.
        """
        # Query the database for a wallet instance that matches the user, wallet type, and amount criteria.

        wallet_query = cls.objects.filter(
            user=user, wallet_type=wallet_type, account=account
        )

        # Determine if the wallet is chargeable (has sufficient funds) and get the first matching wallet instance.
        if wallet_query.exists():
            wallet_instance = wallet_query.first()
        else:
            wallet_instance = None

        result = {
            "is_chargeable": (
                wallet_instance.balance >= amount if wallet_instance else False
            ),
            "balance": wallet_instance.balance if wallet_instance else 0.0,
            "expected_debit": amount,
            "wallet_instance": wallet_instance,
        }
        # logger.info(f"{result}")
        return result

    @classmethod
    def charge_wallet(
        cls, wallet_instance, amount: float, transaction_instance
    ) -> dict:
        if amount > 0:

            wallet_type = wallet_instance.wallet_type
            user = wallet_instance.user

            balance_before = wallet_instance.balance

            transaction_instance.balance_before = balance_before
            # transaction_instance.float_to_internal_ref = reference
            transaction_instance.balance_after = balance_before - amount
            transaction_instance.transfer_stage = TransferStage.DEBIT
            transaction_instance.save()

            # Deduct the amount from the wallet's balance and save the updated balance
            wallet_instance.balance = F("balance") - amount
            wallet_instance.save()
            wallet_instance.refresh_from_db()

            # Record the balance after the charge
            balance_after = wallet_instance.balance

            # Create a debit entry in the DebitCreditRecordOnAccount
            DebitCreditRecordOnAccount.objects.create(
                user=user,
                entry=DebitCreditEntry.DEBIT,
                wallet_type=wallet_type,
                wallet=wallet_instance,
                balance_before=balance_before,
                amount=amount,
                balance_after=balance_after,
                transaction_instance_id=transaction_instance.id,
            )

            return {
                "status": True,
                "balance_before": balance_before,
                "balance_after": balance_after,
                "wallet": wallet_instance,
            }
        else:
            return {
                "status": False,
            }
        
    @classmethod
    def wallet_reversal(cls, transaction_instance: "Transaction"):
        expectd_reversal_status = [TransactionStatus.FAILED, TransactionStatus.REVERSED]
        # print(transaction_instance.is_reversed)
        # print(transaction_instance.status, "\n\n")
        if (
            not transaction_instance.is_reversed
            and transaction_instance.status in expectd_reversal_status
        ):
            user = transaction_instance.user
            wallet_type = transaction_instance.payout_type
            # account system
            account = AccountSystem.objects.filter(
                user=user, account_number=transaction_instance.source_account_number
            ).first()

            wallet_instance = cls.objects.filter(
                user=user, wallet_type=wallet_type, account=account
            ).first()
            if wallet_instance:
                # amount = transaction_instance.debit_amount
                amount = transaction_instance.amount + transaction_instance.commission + transaction_instance.provider_fee - transaction_instance.stamp_duty  # type: ignore

                reversal_transaction = Transaction.objects.create(
                    amount=amount,
                    transaction_type=TransactionType.REVERSAL,
                    total_amount_received=amount,
                    reversal_for=transaction_instance.id,
                    user=transaction_instance.user,
                    company_name=transaction_instance.company_name,
                    user_full_name=transaction_instance.user_full_name,
                    user_email=transaction_instance.user_email,
                    payout_type=transaction_instance.payout_type,
                    beneficiary_account_number=transaction_instance.beneficiary_account_number,
                    bank_name=transaction_instance.bank_name,
                    # narration=transaction_instance.narration,
                    bank_code=transaction_instance.bank_code,
                    transfer_provider=transaction_instance.transfer_provider,
                    source_account_name=transaction_instance.source_account_name,
                    source_account_number=transaction_instance.source_account_number,
                )
                balance_before = wallet_instance.balance

                # Deduct the amount from the wallet's balance and save the updated balance
                wallet_instance.balance = F("balance") + amount  # type: ignore
                wallet_instance.save()  # type: ignore
                wallet_instance.refresh_from_db()  # type: ignore

                # Record the balance after the charge
                balance_after = wallet_instance.balance

                # Create a debit entry in the DebitCreditRecordOnAccount
                DebitCreditRecordOnAccount.objects.create(
                    user=user,
                    entry=DebitCreditEntry.REVERSAL,
                    wallet_type=wallet_type,
                    wallet=wallet_instance,
                    balance_before=balance_before,
                    amount=amount,
                    balance_after=balance_after,
                    transaction_instance_id=transaction_instance.id,
                )

                # transaction_instance.balance_before = balance_before
                # transaction_instance.balance_after = balance_after
                transaction_instance.is_reversed = True
                transaction_instance.save()

                reversal_transaction.balance_before = balance_before
                reversal_transaction.balance_after = balance_after
                reversal_transaction.status = TransactionStatus.SUCCESSFUL
                reversal_transaction.is_reversed = True
                reversal_transaction.save()

                # send reversal mail

                cls.send_reversal_mail(
                    recipient=reversal_transaction.user.email,
                    amount=reversal_transaction.amount,
                    balance_after=reversal_transaction.balance_after,
                    acct_name=reversal_transaction.source_account_name,
                    account_number=reversal_transaction.source_account_number,
                    wallet_type=reversal_transaction.payout_type,
                )

                return {
                    "amount_before": balance_before,
                    "amount_after": balance_after,
                    "wallet_id": str(wallet_instance.id),
                }
            else:
                return None
            
    @classmethod
    def wallet_balance(cls, user: Profile, wallet_type: AccountType):
        wallet = cls.objects.filter(user=user, wallet_type=wallet_type).first()
        if wallet:
            balance = wallet.balance
        else:
            balance = 0
        return balance

class Transaction(models.Model):
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    # User
    user = models.ForeignKey(
        Profile, on_delete=models.CASCADE, related_name="transactions"
    )
    wallet_id = models.CharField(max_length=500, null=True, blank=True)
    user_full_name = models.CharField(max_length=150, null=True, blank=True)
    user_email = models.EmailField(null=True, blank=True)
    user_phone_number = models.CharField(max_length=150, null=True, blank=True)

    amount = models.FloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    commission = models.FloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    # --------------------balances-------------------------
    balance_before = models.FloatField(null=True, blank=True)
    balance_after = models.FloatField(null=True, blank=True)
    # ------------------------------------------------------
    status = models.CharField(
        max_length=150,
        choices=TransactionStatus.choices,
        default=TransactionStatus.PENDING,
    )

    transaction_type = models.CharField(
        max_length=50,
        choices=TransactionType.choices,
        default=TransactionType.DEPOSIT,
    )
    debit_amount = models.FloatField(null=True, blank=True)
    total_amount_received = models.FloatField(null=True, blank=True)
    transfer_provider = models.CharField(
        max_length=50, choices=AcctProvider.choices, null=True, blank=True
    )
    
    transfer_stage = models.CharField(
        max_length=50, choices=TransferStage.choices, null=True, blank=True
    )
    # reference
    provider_fee = models.FloatField(null=True, blank=True)
    stamp_duty = models.FloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    internal_acct_bal = models.FloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    internal_acct_reversal_bal = models.FloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0)]
    )
    internal_to_outwards_session_id = models.CharField(
        max_length=500, editable=False, null=True, blank=True
    )
    float_to_outwards_session_id = models.CharField(
        max_length=500, editable=False, null=True, blank=True
    )
    float_to_internal_session_id = models.CharField(
        max_length=500, editable=False, null=True, blank=True
    )
    transaction_ref = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    float_to_external_ref = models.CharField(max_length=500, null=True, blank=True)
    float_to_internal_ref = models.CharField(max_length=500, null=True, blank=True)
    internal_to_outwards_ref = models.CharField(max_length=500, null=True, blank=True)
    internal_to_float_ref = models.CharField(max_length=500, null=True, blank=True)
    bank_deposit_ref = models.CharField(max_length=500, null=True, blank=True)
    bank_deposit_session_id = models.CharField(max_length=500, null=True, blank=True)
    commission_ref = models.CharField(max_length=500, null=True, blank=True)
    reversal_for = models.CharField(
        max_length=500,
        null=True,
        blank=True,
        help_text="This is the ID that references a transaction that was reversed or failed and is now reversed to the user's wallet.",
    )

    # For BANK_TRANSFER transactions
    beneficiary_account_number = models.CharField(max_length=250, null=True, blank=True)
    beneficiary_account_name = models.CharField(max_length=250, null=True, blank=True)
    bank_name = models.CharField(max_length=250, null=True, blank=True)
    bank_code = models.CharField(max_length=250, null=True, blank=True)
    source_account_name = models.CharField(max_length=250, null=True, blank=True)
    source_account_number = models.CharField(max_length=250, null=True, blank=True)
    total_amount_resolved = models.FloatField(null=True, blank=True)

    payout_type = models.CharField(
        max_length=50,
        choices=AccountType.choices,
        default=AccountType.AIRTIME,
    )

    narration = models.CharField(max_length=1000, null=True, blank=True)
    date_credited = models.DateTimeField(null=True, blank=True)

    # bank_float_balance_before = models.FloatField(null=True, blank=True)
    # bank_float_balance_after = models.FloatField(null=True, blank=True)

    user_uuid = models.CharField(max_length=500, null=True, blank=True)
    company_id = models.CharField(max_length=500, null=True, blank=True)
    # total_amount_charged = models.FloatField(null=True, blank=True)
    float_to_internal = models.BooleanField(default=False)
    internal_to_outwards = models.BooleanField(default=False)
    is_reversed = models.BooleanField(default=False)

    # For AIRTIME_TOPUP transactions
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    network_provider = models.CharField(max_length=100, null=True, blank=True)

    # For FUND_BUDDY transactions
    beneficiary_wallet_id = models.CharField(max_length=250, null=True, blank=True)
    beneficiary_wallet_type = models.CharField(max_length=250, null=True, blank=True)
    source_wallet_id = models.CharField(max_length=250, null=True, blank=True)
    source_wallet_type = models.CharField(max_length=250, null=True, blank=True)
    internal_to_internal_ref = models.CharField(max_length=250, null=True, blank=True)
    fund_internal_buddy_ref = models.CharField(max_length=250, null=True, blank=True)
    wallet_type = models.CharField(max_length=250, null=True, blank=True)
    one_time = models.BooleanField(default=False)

    # VAS SERVICE
    vas_transaction_id = models.CharField(max_length=1000, null=True, blank=True)
    vas_libertypay_reference = models.CharField(max_length=1000, null=True, blank=True)

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "TRANSACTION"
        verbose_name_plural = "TRANSACTIONS"


    def __str__(self):
        return f"{self.user.email} - Transaction id: {self.id}"


    @classmethod
    def create_unique_transaction_ref(cls, suffix):
        epoch = int(time.time())
        ref = f"{suffix}-{str(epoch)[-10:]}-{uuid.uuid4()}"
        return ref

    
    def update_transaction_status(self, status):
        """
        Updates the transaction status and saves it.

        Args:
            status (str): The new status of the transaction.

        Returns:
            None
        """
        self.status = status
        self.save()

    def update_transfer_stage(self, stage: TransferStage):
        """
        Updates the current transfer stage for an instance and saves the change to the database.

        Args:
            stage (str): The new transfer stage to be set.

        Returns:
            None
        """
        self.transfer_stage = stage
        # self.internal_to_outwards = stage == TransferStage.INTERNAL_TO_OUTWARDS
        self.save()


class DebitCreditRecordOnAccount(models.Model):
    user = models.ForeignKey(Profile, on_delete=models.CASCADE)
    entry = models.CharField(max_length=200, choices=DebitCreditEntry.choices)
    wallet_type = models.CharField(
        max_length=255, choices=AccountType.choices, null=True, blank=True
    )
    wallet = models.ForeignKey(Wallet, on_delete=models.SET_NULL, null=True)
    balance_before = models.FloatField(default=0.00)
    balance_after = models.FloatField(default=0.00)
    amount = models.FloatField(validators=[MinValueValidator(0.0)])
    type_of_trans = models.CharField(max_length=200, null=True, blank=True)
    transaction_instance_id = models.CharField(max_length=400, null=True, blank=True)
    date_credited = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    date_created = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.entry.lower()} - {self.wallet_type.lower()}"

    class Meta:
        verbose_name = "DEBIT CREDIT RECORD"
        verbose_name_plural = "DEBIT CREDIT RECORDS"


class TransactionMetaData(models.Model):
    user = models.ForeignKey(Profile, on_delete=models.CASCADE)
    transaction = models.ForeignKey(
        Transaction,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    verification_ref = models.CharField(max_length=255, null=True, blank=True)
    payout_payload = models.TextField(null=True, blank=True)
    fund_wallet_payload = models.TextField(null=True, blank=True)
    payout_result = models.TextField(null=True, blank=True)
    float_verification_result = models.TextField(null=True, blank=True)
    internal_to_external_payload = models.TextField(null=True, blank=True)
    internal_to_external_result = models.TextField(null=True, blank=True)
    internal_verification_result = models.TextField(null=True, blank=True)
    commission_payload = models.TextField(null=True, blank=True)
    commission_result = models.TextField(null=True, blank=True)
    commission_ver_result = models.TextField(null=True, blank=True)
    account_enquiry = models.TextField(null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-date_added"]
        verbose_name = "SEND MONEY LOGS"
        verbose_name_plural = "SEND MONEY LOGS"
    

class CoreBankingCallback(BaseModel):
    one_time = models.BooleanField(default=False)
    request_reference = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    company = models.CharField(max_length=255)
    sub_company = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    sub_company_email = models.EmailField(
        max_length=255,
        null=True,
        blank=True,
    )
    sub_company_unique_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    recipient_account_name = models.CharField(max_length=255)
    recipient_account_number = models.CharField(max_length=255)
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.0)],
    )
    fee = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.0)],
    )
    amount_payable = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.0)],
    )
    reference = models.CharField(max_length=255, unique=True)
    transaction_type = models.CharField(max_length=255)
    payer_account_name = models.CharField(max_length=255)
    payer_account_number = models.CharField(max_length=255)
    payer_bank_code = models.CharField(max_length=255)
    paid_at = models.CharField(max_length=255)
    narration = models.CharField(max_length=255)
    session_id = models.CharField(max_length=255, unique=True)
    transaction_reference = models.CharField(max_length=255, unique=True)
    settlement_status = models.BooleanField(
        default=False,
        editable=False,
        help_text="indicates if the inflow has settled in the company's/branch's wallet.",
    )
    currency = models.CharField(max_length=25)
    payload = models.TextField(null=True, blank=True)

    def __str__(self) -> str:
        return f"sessionID: {self.session_id} && amount: {self.amount}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CORE BANKING CALLBACK"
        verbose_name_plural = "CORE BANKING CALLBACKS"
        ordering = ["-created_at"]

    @classmethod
    def register_wema_inflow(cls, data):
        try:
            inflow = cls.objects.create(
                one_time=data.get("one_time"),
                request_reference=data.get("request_reference"),
                company=data.get("company"),
                sub_company=data.get("sub_company"),
                sub_company_email=data.get("sub_company_email"),
                sub_company_unique_id=data.get("sub_company_unique_id"),
                recipient_account_name=data.get("recipient_account_name"),
                recipient_account_number=data.get("recipient_account_number"),
                amount=data.get("amount"),
                fee=data.get("fee"),
                amount_payable=data.get("amount_payable"),
                reference=data.get("reference"),
                transaction_type=data.get("transaction_type"),
                payer_account_name=data.get("payer_account_name"),
                payer_account_number=data.get("payer_account_number"),
                payer_bank_code=data.get("payer_bank_code"),
                paid_at=data.get("paid_at"),
                narration=data.get("narration"),
                session_id=data.get("session_id"),
                transaction_reference=data.get("transaction_reference"),
                currency=data.get("currency"),
                payload=json.dumps(data),
            )
            handle_company_wema_inflow(inflow.id)
            response = {"message": "event notification received."}
        except IntegrityError as inflow_error:
            response = {"message": str(inflow_error)}
        return response
    

class VASTransaction(BaseModel):
    """
    Represents a transaction in the VAS system.
    """
    user = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name="vas_transactions")
    customer_reference = models.CharField(max_length=255, null=True, blank=True, unique=True)
    libertypay_reference = models.CharField(max_length=255, null=True, blank=True)
    amount = models.FloatField(null=True, blank=True, validators=[MinValueValidator(30.0)])
    status = models.CharField(max_length=50, choices=TransactionStatus.choices, default=TransactionStatus.PENDING)
    bill_provider = models.CharField(max_length=200, choices=BillProvider.choices, default=BillProvider.CORALPAY)

    customer_id = models.CharField(max_length=100, null=True, blank=True)
    package_slug = models.CharField(max_length=100, null=True, blank=True)
    channel = models.CharField(max_length=100, null=True, blank=True)
    customer_name = models.CharField(max_length=100, null=True, blank=True)
    phone_number = models.CharField(max_length=100, null=True, blank=True)
    bills_type = models.CharField(max_length=200, choices=enums.BillsType.choices, default=BillsType.OTHER)
    biller = models.CharField(max_length=100, null=True, blank=True)
    vas_request = models.TextField(null=True, blank=True)
    vas_response = models.TextField(null=True, blank=True)
    whatsapp_session = models.ForeignKey(
        "vendboss.WhatsAppSession", 
        on_delete=models.CASCADE, 
        related_name="vas_whatsapp_session",
        blank=True,
        null=True
    )
    vas_initiated = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "VAS TRANSACTION"
        verbose_name_plural = "VAS TRANSACTIONS"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.user.phone} - {self.customer_reference}"
    
    @classmethod
    def create_transaction(
            cls, user: Profile, customer_reference: str, amount: float,
            provider: BillProvider, customer_id: str,
            package_slug: str, channel: str, customer_name: str,
            phone_number: str, bills_type: BillsType, biller: str) -> "VASTransaction":
        """
        Create a new VAS transaction entry.
        """
        transaction = cls.objects.create(
            user=user,
            customer_reference=customer_reference,
            amount=amount,
            bill_provider=provider,
            customer_id=customer_id,
            package_slug=package_slug,
            channel=channel,
            customer_name=customer_name,
            phone_number=phone_number,
            bills_type=bills_type,
            biller=biller
        )
        return transaction
    
    @classmethod
    def initiate_whatsapp_transaction(
        cls,
        customer_id: str,
        amount: float,
        package_slug: str,
        bills_type: str,
        biller: str,
        phone_number: str,
        user_code: str,
        session : Optional[WhatsAppSession] = None

        ):
        
        user = UserRegistration.fetch_session_user(phone_number=phone_number, whatsapp_id=user_code)
        if user:
            balance = Wallet.wallet_balance(user=user, wallet_type="GENERAL")
            if balance >= amount:
                reference = buy_utility_bills(
                    user_id=user.id, 
                    amount=amount, 
                    provider="CORALPAY", 
                    customer_id=customer_id, 
                    package_slug=package_slug, 
                    customer_name=user.full_name, 
                    phone_number=phone_number, 
                    bills_type=bills_type, 
                    biller=biller
                )
                vas_transaction = cls.objects.filter(customer_reference=reference).first()
                if vas_transaction:
                    vas_transaction.whatsapp_session = session
                    vas_transaction.save()

                message = f"Processing Transaction... 🔄⏳!\n\nReference: {reference}\n"
            else:
                message = f"⚠️ Insufficient Balance ❌"
        else:
            user = UserRegistration.fetch_associated_user(whatsapp_id=user_code)
            reference = vas_transaction_reference()
            if user:
                owner = user
            else:
                vas_user_id = f"{settings.VAS_USER_ID}"
                owner = Profile.objects.get(id=vas_user_id)

            vas_transaction = cls.create_transaction(
                user=owner,
                customer_reference=reference,
                amount=amount, 
                provider="CORALPAY", 
                customer_id=customer_id,
                package_slug=package_slug, 
                channel="WEB", 
                customer_name=owner.full_name,
                phone_number=phone_number, 
                bills_type=bills_type, 
                biller=biller
            )
            vas_transaction.whatsapp_session = session
            vas_transaction.save()

            response = WemaApi.get_one_time_account(request_reference=reference)
            account_details = response.get("data", {}).get("data", {}).get("account_details", {})
            bank_name = account_details.get("bank_name")
            account_name = account_details.get("account_name")
            account_number = account_details.get("account_number")
            request_reference = account_details.get("request_reference")
            if response.get("status_code") != 200:
                message = f"An error Occured, Please try again! ❌🚫"
            else:
                message = f"Pay ₦{amount} into the following account!\n\nAccount No: {account_number}"
                message += f"\nAccount Name: {account_name}\nBank Name: {bank_name}\nReference: {request_reference}\n"
                message += f"Amount: ₦{amount}"

        return message
    
    @classmethod
    def initiate_web_transaction(
        cls,
        customer_id: str,
        amount: float,
        package_slug: str,
        bills_type: str,
        biller: str,
        phone_number: str,
        user_code: str,
        ):
        
        # user = UserRegistration.fetch_session_user(phone_number=phone_number, whatsapp_id=user_code)
        # if user:
        #     balance = Wallet.wallet_balance(user=user, wallet_type="GENERAL")
        #     if balance >= amount:
        #         reference = buy_utility_bills(
        #             user_id=user.id, 
        #             amount=amount, 
        #             provider="CORALPAY", 
        #             customer_id=customer_id, 
        #             package_slug=package_slug, 
        #             customer_name=user.full_name, 
        #             phone_number=phone_number, 
        #             bills_type=bills_type, 
        #             biller=biller
        #         )

        #         message = f"Processing Transaction... 🔄⏳!\n\nReference: {reference}\n"
        #     else:
        #         message = f"⚠️ Insufficient Balance ❌"
        # else:

        user = UserRegistration.fetch_associated_user(whatsapp_id=user_code)
        reference = vas_transaction_reference()
        if user:
            owner = user
        else:
            vas_user_id = f"{settings.VAS_USER_ID}"
            owner = Profile.objects.get(id=vas_user_id)

        vas_transaction = cls.create_transaction(
            user=owner,
            customer_reference=reference,
            amount=amount, 
            provider="CORALPAY", 
            customer_id=customer_id,
            package_slug=package_slug, 
            channel="WEB", 
            customer_name=owner.full_name,
            phone_number=phone_number, 
            bills_type=bills_type, 
            biller=biller
        )

        response = WemaApi.get_one_time_account(request_reference=reference)
        account_details = response.get("data", {}).get("data", {}).get("account_details", {})
        bank_name = account_details.get("bank_name")
        account_name = account_details.get("account_name")
        account_number = account_details.get("account_number")
        request_reference = account_details.get("request_reference")
        if response.get("status_code") != 200:
            data = {
                "status": False,
                "message": "An error Occured, Please try again!",
                "amount": 0,
                "account_name": "",
                "account_number": "",
                "bank_name": "",
                "reference": ""
            }
        else:
            data = {
                "status": True,
                "message": f"Pay ₦{amount} into the following account!",
                "amount": amount,
                "account_name": account_name,
                "account_number": account_number,
                "bank_name": bank_name,
                "reference": request_reference
            }

        return data
    
    @classmethod
    def initiate_web_session_transaction(
        cls,
        customer_id: str,
        amount: float,
        package_slug: str,
        bills_type: str,
        biller: str,
        phone_number: str,
        user_code: str,
        ):
        
        user = UserRegistration.fetch_session_user(phone_number=phone_number, whatsapp_id=user_code)
        balance = Wallet.wallet_balance(user=user, wallet_type="GENERAL")
        if balance >= amount:
            reference = buy_utility_bills(
                user_id=user.id, 
                amount=amount, 
                provider="CORALPAY", 
                customer_id=customer_id, 
                package_slug=package_slug, 
                customer_name=user.full_name, 
                phone_number=phone_number, 
                bills_type=bills_type, 
                biller=biller
            )
            data = {
                "status": True,
                "message": "Processing Transaction... ",
                "reference": reference
            }
        else:
            data = {
                "status": False,
                "message": "Insufficient Balance",
                "reference": ""
            }
        return data
