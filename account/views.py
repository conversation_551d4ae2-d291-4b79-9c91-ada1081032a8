from django.shortcuts import render
from drf_yasg.utils import swagger_auto_schema
from rest_framework import filters, generics, status
from rest_framework.decorators import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from account.models import CoreBankingCallback



# Create your views here.

class WemaWebhookAPIView(APIView):

    def post(self, request, *args, **kwargs):
        if request.data == {}:
            return Response(
                {
                    "error": True,
                    "message": "invalid request, cannot handle empty data.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        data = request.data
        response = CoreBankingCallback.register_wema_inflow(data=data)
        return Response(
            response,
            status=status.HTTP_200_OK,
        )