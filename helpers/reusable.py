import base64
import os
from string import (
    Template,
    punctuation,
)
from typing import Optional

from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.paginator import Paginator as django_core_paginator
from django.utils.translation import gettext_lazy as text
from requests import exceptions, request


PASSWORD = settings.SECRET_PASSWORD.encode()
SALT = "staticsalt".encode()


# Helper function(s) and object(s).
def validate_password(password: str):
    """
    Validates the given password based on the criteria.
    Args:
        password (str): The password string to be validated.
    Raises:
        ValidationError:
        if the password fails to meet any of the following criteria:
        - The length of the password is less than 8 characters.
        - The password does not contain at least one numeric digit.
        - The password does not contain at least one uppercase character.
        - The password does not contain at least one lowercase character.
        - The password does not contain at least one special character.
    Returns:
        bool: True if the password passes all validation criteria.
    """
    special_characters = list(punctuation)

    if len(password) < 8:
        raise ValidationError(
            text("Error: the password length cannot be less than 8 characters.")
        )
    if not any(char.isdigit() for char in password):
        raise ValidationError(
            text("Error: the password should have at least one numeric digit.")
        )
    if not any(char.isupper() for char in password):
        raise ValidationError(
            text("Error: the password should have at least one uppercase character.")
        )
    if not any(char.islower() for char in password):
        raise ValidationError(
            text("Error: the password should have at least one lowercase character.")
        )
    if not any(char in special_characters for char in password):
        raise ValidationError(
            text("Error: the password should have at least one special character.")
        )
    return True


def add_prefix_to_phone(phone: str):
    """
    Returns phone number with Nigeria country code.
    """
    if len(phone) >= 11:
        phone = f"234{str(phone)[-10:]}"
        return phone
    raise ValidationError(text("Error: invalid phone number."))


def make_request(request_type: str, params: dict) -> dict:
    """
    Make an HTTP request using the specified request_type and parameters.
    Args:
        request_type (str): The type of HTTP request to make (e.g., 'GET', 'POST', 'PUT', 'DELETE', etc.).
        params (dict): A dictionary containing the parameters to be passed in the HTTP request.
    Returns:
        dict: A dictionary containing the response status, data, and error details.
        - 'status': A boolean indicating if the request was successful (True) or not (False).
        - 'data': A dictionary containing the JSON response data if the request was successful, otherwise None.
        - 'error': A dictionary containing error details if the request failed, otherwise None.
    """
    try:
        response = request(request_type, **params)
        try:
            data = response.json()
        except exceptions.JSONDecodeError:
            data = response.text
        return {
            "status_code": response.status_code,
            "status": True,
            "data": data,
            "error": None,
        }
    except exceptions.RequestException as error:
        return {
            "status_code": 500,
            "status": False,
            "data": None,
            "error": str(error),
        }


def email_sender(
    recipient: list,
    subject: str,
    template_directory: Optional[str] = None,
    file=None,
    file_name=None,
    use_template: Optional[bool] = False,
    has_attachment: Optional[bool] = False,
    text: Optional[str] = None,
    **substitutes,
):
    """
    Send an email to one or more recipients using Mailgun API.
    Args:
    recipient (list): A list of email addresses of the recipients.
    subject (str): The subject of the email.
    template_directory (str, optional): The directory containing the email HTML template file. Defaults to None.
    file (object, optional): The file to be attached to the email. Defaults to None.
    file_name (str, optional): The name of the attached file. Defaults to None.
    use_template (bool, optional): Flag indicating whether to use an HTML template for the email body. Defaults to True.
    has_attachment (bool, optional): Flag indicating whether the email has an attachment. Defaults to False.
    text (str, optional): The plain-text body of the email. Used when `use_template` is False. Defaults to None.
    **substitutes: Keyword arguments used for substituting variables in the email template if `use_template` is True.
    Returns:
    dict: A dictionary containing the Mailgun API response. The response can be retrieved using the 'email_sender_response' key.
    """

    url = settings.MAILGUN_URL
    api_key = settings.MAILGUN_API_KEY

    if use_template and has_attachment:
        TEMPLATE_DIR = os.path.join("templates", template_directory)
        html_temp = os.path.abspath(TEMPLATE_DIR)

        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(substitutes)

        response = make_request(
            "POST",
            dict(
                url=url,
                auth=("api", api_key),
                data={
                    "from": "LibertyPay <<EMAIL>>",
                    "to": recipient,
                    "subject": subject,
                    "html": template,
                },
                files=[("attachment", (file_name, open(str(file), "rb").read()))],
            ),
        )
    elif use_template:
        TEMPLATE_DIR = os.path.join("templates", template_directory)
        html_temp = os.path.abspath(TEMPLATE_DIR)

        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(substitutes)

        response = make_request(
            "POST",
            dict(
                url=url,
                auth=("api", api_key),
                data={
                    "from": "LibertyPay <<EMAIL>>",
                    "to": recipient,
                    "subject": subject,
                    "html": template,
                },
            ),
        )
    else:
        response = make_request(
            "POST",
            dict(
                url=url,
                auth=("api", api_key),
                data={
                    "from": "LibertyPay <<EMAIL>>",
                    "to": recipient,
                    "subject": subject,
                    "text": text,
                },
            ),
        )
    if response.get("status") == True:
        return dict(email_sender_response=response)
    else:
        return dict(email_sender_response=response)


class CryptographyService:
    """
    Encrypt & Decrypt API Key(s).
    """

    # KDF: Key Derivation Function.
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=SALT,
        iterations=100000,
        backend=default_backend(),
    )
    key = base64.urlsafe_b64encode(kdf.derive(PASSWORD))
    secret_key = Fernet(key)

    def encrypt(self, plain_text):
        encrypted = self.secret_key.encrypt(plain_text.encode())
        return encrypted.decode()

    def decrypt(self, cipher_text, encoding="ascii"):
        decrypted = self.secret_key.decrypt(cipher_text.encode())
        return decrypted.decode()


class Paginator:
    """
    This class provides a method to paginate a queryset based on the request parameters.
    """

    @staticmethod
    def paginate(request, queryset):
        """
        This method takes the request object and the queryset to be paginated as input.
        It reads the pagination parameters from the request and paginates the queryset accordingly.
        NOTE:
        - the 'size' parameter defines the number of items per page.
        - the 'page' parameter determines the requested page number.
        Args:
            request (HttpRequest): The HTTP request object containing pagination parameters.
            queryset (QuerySet): The Django queryset to be paginated.
        Returns:
            Page: A Django Page object containing the paginated results.
        """
        query_parameters = request.GET
        paginator = django_core_paginator(
            queryset,
            int(query_parameters.get("size", 100)),
        )
        requested_page = int(query_parameters.get("page", 1))
        verified_page = (
            requested_page
            if requested_page < paginator.num_pages
            else paginator.num_pages
        )
        page = paginator.page(verified_page)
        return page


def convert_string_to_base32(text: str):
    """
    Convert a given string to a base32-encoded string.
    Args:
        text (str): The input string that needs to be encoded.
    Returns:
        str: The base32-encoded string representing the input text.
    """
    encoded_bytes = base64.b32encode(text.encode())
    encoded_string = encoded_bytes.decode()
    return encoded_string



def whatsapp_redirect_link(whatsapp_id: str):
    from users.models import UserRegistration
    from urllib.parse import urlencode

    try:
        user_whatsapp_url = UserRegistration.objects.get(whatsapp_id=whatsapp_id)
    except UserRegistration.DoesNotExist:
        whatsapp_link = ""
        return whatsapp_link
        
    phone_number = user_whatsapp_url.phone_number
    format_message = {
        'text': f'{whatsapp_id}'
    }
    formatted_url_message = urlencode(format_message)
    whatsapp_link = f"https://wa.me/{phone_number}?{formatted_url_message}"
    return whatsapp_link