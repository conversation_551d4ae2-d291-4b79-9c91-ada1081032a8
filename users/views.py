from django.shortcuts import render
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema
from helpers.libertypay import LibertyPay
from rest_framework import status
from rest_framework.decorators import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from users.models import UserRegistration
from users.serializers import UserRegistrationSerializer
# Create your views here.


class RegistrationAPIView(APIView):
    """Register a new user."""
    serializer_class = UserRegistrationSerializer

    @swagger_auto_schema(request_body=UserRegistrationSerializer)
    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data.get('phone_number')
        first_name = serializer.validated_data.get('first_name')
        last_name = serializer.validated_data.get('last_name')
        password = serializer.validated_data.get('password')
        store_name = serializer.validated_data.get('store_name')
        email = serializer.validated_data.get('email')
        state = serializer.validated_data.get('state')

        UserRegistration.create_registration(
            phone_number=phone_number,
            email=email,
            first_name=first_name,
            last_name=last_name,
            password=password,
            store_name=store_name,
            state=state,
        )
        
        response = UserRegistration.fetch_merchant_details(phone_number=phone_number)
        return Response(
            data=response,
            status=status.HTTP_201_CREATED,
        )
    

class FetchUserDetailsAPIView(APIView):
    permission_classes = [IsAuthenticated]

    """Fetch User Details"""
    def get(self, request):
        user_phone_number = request.user.phone
        
        response = UserRegistration.fetch_merchant_details(phone_number=user_phone_number)
        return Response(
            data=response,
            status=status.HTTP_201_CREATED,
        )
    

class BillerListAPIView(APIView):
    # permission_classes = [IsAuthenticated]

    """Fetch User Details"""
    def get(self, request):
        response = LibertyPay.biller_list()
        return Response(
            data=response,
            status=status.HTTP_201_CREATED,
        )
    

# class TransactionStatusAPIView(APIView):
#     """Fetch User Details"""
#     def get(self, request):
#         transaction_id = request.query_params.get("transaction_id")
#         response = LibertyPay.bills_payment_status(transaction_id=transaction_id)
#         return Response(
#             data=response,
#             status=status.HTTP_201_CREATED,
#         )