
# urls.py
from django.urls import path
from vendboss import views

urlpatterns = [
    path('venbox_user_profiles/', views.VendboxProfileCreateAPIView.as_view(), name='profile-create'),
    path('webhook/', views.WhatsAppWebhookAPIView.as_view(), name='webhook'),

    # Session management endpoints
    path('sessions/', views.WhatsAppSessionListAPIView.as_view(), name='session-list'),
    path('sessions/<uuid:session_id>/', views.WhatsAppSessionDetailAPIView.as_view(), name='session-detail'),

    # Biller provider
    path('provider/airtime/', views.AirtimeProviderAPIView.as_view(), name='airtime'),
    path('provider/data/', views.DataProviderAPIView.as_view(), name='data'),
    path('provider/cable/', views.CableProviderAPIView.as_view(), name='cable'),
    path('provider/electric/', views.ElectricProviderAPIView.as_view(), name='electric'),
    path('provider/bet/', views.BetProviderAPIView.as_view(), name='bet'),

    # Biller provider options
    path('provider/airtime_list/', views.AirtimeListAPIView.as_view(), name='airtime_list'),
    path('provider/data_list/', views.DataListAPIView.as_view(), name='data_list'),
    path('provider/cable_list/', views.CableListAPIView.as_view(), name='cable_list'),
    path('provider/electric_list/', views.ElectricListAPIView.as_view(), name='electric_list'),
    path('provider/bet_list/', views.BetListAPIView.as_view(), name='bet_list'),

    # VAS Payments
    path('provider/bills_payment/', views.BillPaymentAPIView.as_view(), name='bills_payment'),
    path('provider/bills_payment_session/', views.BillPaymentSesionAPIView.as_view(), name='bills_payment_session'),

    # History
    path('history/transactions/', views.TransactionHistoryAPIView.as_view(), name='transaction-history'),


]
