# models.py
from django.db import models
from django.utils import timezone
from datetime import timedelta
import uuid
from main.models import BaseModel
import random
import string


class GeneralAccount(BaseModel):
    """General account model"""
    name = models.Char<PERSON>ield(max_length=100)
    phone_number = models.Char<PERSON>ield(max_length=15)
    account_number = models.CharField(max_length=20)
    bank_name = models.Char<PERSON>ield(max_length=100)

    class Meta:
        db_table = 'general_account'
        verbose_name = 'General Account'
        verbose_name_plural = 'General Accounts'
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.name} - {self.bank_name}"


class AirtimeAccount(BaseModel):
    """Airtime account model"""
    name = models.Char<PERSON>ield(max_length=100)
    phone_number = models.CharField(max_length=15)
    account_number = models.Char<PERSON><PERSON>(max_length=20)
    bank_name = models.Char<PERSON><PERSON>(max_length=100)

    class Meta:
        db_table = 'airtime_account'
        verbose_name = 'Airtime Account'
        verbose_name_plural = 'Airtime Accounts'

    def __str__(self):
        return f"{self.name} - {self.bank_name}"


class VendboxUserProfile(BaseModel):
    """Vendbox user profile model"""
    phone_number = models.CharField(max_length=15, unique=True)
    whatsapp_url = models.URLField(max_length=255, blank=True, null=True)
    web_url = models.URLField(max_length=255, blank=True, null=True)
    general_account = models.ForeignKey(
        GeneralAccount,
        on_delete=models.CASCADE,
        related_name='vendbox_profiles'
    )
    airtime_account = models.ForeignKey(
        AirtimeAccount,
        on_delete=models.CASCADE,
        related_name='vendbox_profiles'
    )
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'vendbox_user_profile'
        verbose_name = 'Vendbox User Profile'
        verbose_name_plural = 'Vendbox User Profiles'

    def __str__(self):
        return f"{self.name} - {self.phone_number}"

class WhatsAppSession(BaseModel):
    """Model to manage WhatsApp user sessions"""
    SESSION_STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('EXPIRED', 'Expired'),
        ('COMPLETED', 'Completed'),
    ]

    session_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    phone_number = models.CharField(max_length=15, db_index=True)
    user_code = models.CharField(max_length=200)
    status = models.CharField(max_length=20, choices=SESSION_STATUS_CHOICES, default='ACTIVE')
    current_screen = models.CharField(max_length=50, default='MAIN_MENU')
    session_data = models.JSONField(default=dict, blank=True)
    expires_at = models.DateTimeField()

    class Meta:
        db_table = 'whatsapp_sessions'
        verbose_name = 'WhatsApp Session'
        verbose_name_plural = 'WhatsApp Sessions'
        indexes = [
            models.Index(fields=['phone_number', 'status']),
            models.Index(fields=['expires_at']),
        ]
        ordering = ["-created_at"]


    def save(self, *args, **kwargs):
        """Set expiration time on creation"""
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(minutes=15)
        super().save(*args, **kwargs)

    def is_expired(self):
        """Check if session has expired"""
        return timezone.now() > self.expires_at or self.status == "EXPIRED"

    def extend_session(self, minutes=15):
        """Extend session expiration time"""
        self.expires_at = timezone.now() + timedelta(minutes=minutes)
        self.save(update_fields=['expires_at', 'updated_at'])

    def __str__(self):
        return f"Session {self.session_id} - {self.phone_number}"


class WhatsAppScreen(BaseModel):
    """Model to track screen navigation history"""
    session = models.ForeignKey(WhatsAppSession, on_delete=models.CASCADE, related_name='screens')
    screen_name = models.CharField(max_length=50)
    screen_data = models.JSONField(default=dict, blank=True)
    user_input = models.TextField(blank=True, null=True)
    response_sent = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'whatsapp_screens'
        verbose_name = 'WhatsApp Screen'
        verbose_name_plural = 'WhatsApp Screens'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.session.phone_number} - {self.screen_name}"


class WhatsAppTransaction(BaseModel):
    """Model to track transaction attempts"""
    TRANSACTION_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('SUCCESS', 'Success'),
        ('FAILED', 'Failed'),
    ]

    session = models.ForeignKey(WhatsAppSession, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=50)  # 'AIRTIME', 'DATA', etc.
    amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    recipient = models.CharField(max_length=15, null=True, blank=True)
    status = models.CharField(max_length=20, choices=TRANSACTION_STATUS_CHOICES, default='PENDING')
    transaction_ref = models.CharField(max_length=100, unique=True, null=True, blank=True)
    response_data = models.JSONField(default=dict, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'whatsapp_transactions'
        verbose_name = 'WhatsApp Transaction'
        verbose_name_plural = 'WhatsApp Transactions'

    def __str__(self):
        return f"{self.transaction_type} - {self.status} - {self.session.phone_number}"
