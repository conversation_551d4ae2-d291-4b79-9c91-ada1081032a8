from django.contrib.auth import get_user_model
from import_export import resources

from users import models


User = get_user_model()


# Create your resource(s) here.
class UserResource(resources.ModelResource):
    class Meta:
        model = User


class AssociatedUserResource(resources.ModelResource):
    class Meta:
        model = models.AssociatedUser


class UserRegistrationResource(resources.ModelResource):
    class Meta:
        model = models.UserRegistration


class ConstantTableResource(resources.ModelResource):
    class Meta:
        model = models.ConstantTable